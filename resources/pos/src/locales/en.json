{"dashboard.title": "Dashboard", "header.pos.title": "POS", "header.profile-menu.profile.label": "Profile", "header.profile-menu.change-password.label": "Change Password", "header.profile-menu.logout.label": "Logout", "product.categories.title": "Product Categories", "expense.categories.title": "Expense Categories", "dashboard.salesReturn.title": "Sales Return", "dashboard.top-customers.title": "Top 5 Customers", "dashboard.purchaseReturn.title": "Purchases Return", "dashboard.ThisWeekSales&Purchases.title": "This Week Sales & Purchases", "dashboard.TopSellingProducts.title": "Top Selling Products", "dashboard.stockAlert.title": "<PERSON>", "dashboard.recentSales.title": "Recent Sales", "dashboard.PaymentSentReceived.title": "Payment Sent & Received", "dashboard.stockAlert.code.label": "Code", "dashboard.stockAlert.product.label": "Product", "dashboard.stockAlert.warehouse.label": "Warehouse", "dashboard.stockAlert.quantity.label": "Quantity", "dashboard.stockAlert.alertQuantity.label": "Alert Quantity", "dashboard.grantTotal.label": "Grand Total", "dashboard.recentSales.reference.label": "REFERENCE", "dashboard.recentSales.customer.label": "Customer", "dashboard.recentSales.status.label": "Status", "dashboard.recentSales.paid.label": "Paid", "dashboard.recentSales.due.label": "Due", "dashboard.recentSales.paymentStatus.label": "Payment Status", "dataTable.searchBar.placeholder.label": "Search", "settings.select.language.label": "Language", "settings.select.language.placeholder": "Select Language", "users.title": "Users", "user.create.title": "Create User", "user.edit.title": "Edit User", "user.input.first-name.label": "First Name", "user.input.last-name.label": "Last Name", "user.input.email.label": "Email", "user.input.phone-number.label": "Phone Number", "user.input.password.label": "Password", "user.input.confirm-password.label": "Confirm Password", "user.input.role.label": "Role", "user.input.first-name.placeholder.label": "Enter First Name", "user.input.last-name.placeholder.label": "Enter Last Name", "user.input.email.placeholder.label": "<PERSON><PERSON>", "user.input.phone-number.placeholder.label": "Enter Phone Number", "user.input.password.placeholder.label": "Enter Password", "user.input.confirm-password.placeholder.label": "Enter Confirm Password", "user.input.role.placeholder.label": "Choose <PERSON>", "users.table.user.column.title": "User", "users.table.phone-number.column.title": "Phone Number", "users.table.role.column.title": "Role", "users.table.date.column.title": "Created date", "user-details.title": "User Details", "user-details.table.created-on.row.label": "Created On", "user-details.table.title": "Overview", "user.input.first-name.validate.label": "Please enter first name", "user.input.last-name.validate.label": "Please enter last name", "user.input.email.validate.label": "Please enter email address", "user.input.email.valid.validate.label": "Please enter valid email address", "user.input.phone-number.validate.label": "Please enter phone number", "user.input.password.validate.label": "Please enter password", "user.input.confirm-password.validate.label": "Please enter confirm password", "user.input.role.validate.label": "Please select role", "user.success.create.message": "User created successfully", "user.success.edit.message": "User updated successfully", "user.success.delete.message": "User deleted successfully", "react-data-table.searchbar.placeholder": "Search", "react-data-table.action.column.label": "Action", "react-data-table.date.column.label": "Date", "react-data-table.no-record-found.label": "There are no records to display", "react-data-table.records-per-page.label": "Records per page", "delete-modal.title": "Delete!", "delete-modal.msg": "Are you sure want to delete this", "delete-modal.yes-btn": "Yes, Delete!", "delete-modal.no-btn": "No, Cancel", "suppliers.title": "Suppliers", "supplier.title": "Supplier", "supplier.create.title": "Create Supplier", "supplier.edit.title": "Edit Supplier", "supplier.table.name.column.title": "Name", "supplier.table.phone-number.column.title": "Phone Number", "supplier.table.email.column.title": "Email", "supplier.table.address.column.title": "Address", "supplier.success.create.message": "Supplier created successfully", "supplier.success.edit.message": "Supplier updated successfully", "supplier.success.delete.message": "Supplier deleted successfully", "globally.input.name.label": "Name", "globally.react-table.column.created-date.label": "Created On", "globally.react-table.column.payment-type.label": "Payment Type", "globally.input.name.placeholder.label": "Enter Name", "globally.input.email.label": "Email", "globally.input.email.placeholder.label": "<PERSON><PERSON>", "globally.input.phone-number.label": "Phone Number", "globally.input.phone-number.placeholder.label": "Enter Phone Number", "globally.input.country.label": "Country", "globally.input.country.placeholder.label": "Enter Country", "globally.input.city.label": "City", "globally.input.city.placeholder.label": "Enter City", "globally.input.address.label": "Address", "globally.input.address.placeholder.label": "Enter Address", "globally.input.notes.label": "Note", "globally.input.notes.placeholder.label": "Enter Note", "globally.loading.label": "Please wait...", "globally.input.name.validate.label": "Please enter name", "globally.input.email.validate.label": "Please enter email address", "globally.input.email.valid.validate.label": "Please enter valid email address", "globally.input.country.validate.label": "Please enter country", "globally.input.city.validate.label": "Please enter city", "globally.input.phone-number.validate.label": "Please enter phone number", "globally.input.address.validate.label": "Please enter address", "globally.input.notes.validate.label": "The note must not be greater than 100 characters", "globally.require-input.validate.label": "This field is required", "globally.date.validate.label": "Please enter date", "globally.tax-length.validate.label": "The Tax must not be greater than 100", "globally.discount-length.validate.label": "The Discount must not be greater than 100", "globally.discount-cost-length.validate.label": "The Discount must not be greater than the product cost", "globally.discount-price-length.validate.label": "The Discount must not be greater than the product price", "globally.type.label": "Type", "globally.back-btn": "Back", "globally.save-btn": "Save", "globally.cancel-btn": "Cancel", "globally.edit-btn": "Edit", "globally.submit-btn": "Submit", "globally.edit.tooltip.label": "Edit", "globally.delete.tooltip.label": "Delete", "globally.view.tooltip.label": "View", "globally.pdf.download.label": "Download PDF", "globally.product-quantity.validate.message": "Please add quantity of product", "globally.product-already-added.validate.message": "This Product Already Added", "globally.detail.company.info": "Company Info", "globally.detail.reference": "Reference", "globally.detail.status": "Status", "globally.detail.warehouse": "Warehouse", "globally.detail.payment.status": "Payment Status", "globally.detail.payment.details": "Payment Details", "globally.detail.order.summary": "Order Summary", "globally.detail.product": "Product", "globally.detail.net-unit-cost": "Net Unit Cost", "globally.detail.net-unit-price": "Net Unit Price", "globally.detail.quantity": "Quantity", "globally.detail.unit-cost": "Unit Cost", "globally.detail.unit-price": "Unit Price", "globally.detail.discount": "Discount", "globally.detail.tax": "Tax", "globally.show.payment.label": "Show Payments", "globally.detail.subtotal": "Subtotal", "globally.detail.order.tax": "Order Tax", "globally.detail.shipping": "Shipping", "globally.detail.grand.total": "Grand Total", "globally.detail.paid": "Paid", "globally.search.field.label": "Search Product by Code Name", "globally.detail.due": "Due", "globally.status.validate.label": "Please select status", "globally.payment.status.validate.label": "Please select payment status", "customers.title": "Customers", "customer.title": "Customer", "customer.create.title": "Create Customer", "customer.edit.title": "Edit Customer", "customer.success.create.message": "Customer created successfully", "customer.success.edit.message": "Customer updated successfully", "customer.success.delete.message": "Customer deleted successfully", "warehouse.title": "Warehouse", "warehouse.create.title": "Create Warehouse", "warehouse.edit.title": "Edit Warehouse", "warehouse.details.title": "Warehouse Details", "warehouse.input.zip-code.label": "Zip Code", "warehouse.input.zip-code.placeholder.label": "Enter Zip Code", "warehouse.input.zip-code.validate.label": "Please enter zip code", "warehouse.input.zip-code.valid.validate.label": "Please enter valid zip code", "warehouse.success.create.message": "Warehouse created successfully", "warehouse.success.edit.message": "Warehouse updated successfully", "warehouse.success.delete.message": "Warehouse deleted successfully", "variation.name": "Variation Name", "variation.variation_types": "Variation Types", "variations.title": "Variations", "variation.title": "Variation", "variation.create.title": "Create Variation", "variation.edit.title": "Edit Variation", "variation.input.name.label": "Name", "variation.input.name.placeholder.label": "Enter Name", "variation.input.name.validate.label": "Please enter name", "variation.success.create.message": "Variation created successfully", "variation.success.edit.message": "Variation updated successfully", "variation.success.delete.message": "Variation deleted successfully", "variation.types.title": "Variation Types", "variation.type.title": "Variation Type", "variation.type.input.name.placeholder.label": "Please enter variation type", "variation.type.input.name.validate.label": "Please enter valid variation types", "variation.select.validation.error.message": "Please select a variation", "variation.type.select.validate.error.message": "Please select variation types", "products.title": "Products", "product.title": "Product", "product.create.title": "Create Product", "product.edit.title": "Edit Product", "product.input.code.label": "Code", "product.input.code.placeholder.label": "Enter Code", "product.input.product-category.label": "Product Category", "product.input.product-category.placeholder.label": "Choose Product Category", "product.input.brand.label": "Brand", "product.input.brand.placeholder.label": "<PERSON><PERSON>", "product.input.barcode-symbology.label": "Barcode Symbology", "product.input.barcode-symbology.placeholder.label": "Choose Barcode Symbology", "product.input.product-cost.label": "Product Cost", "product.input.product-cost.placeholder.label": "Enter Product Cost", "product.input.product-price.label": "Product Price", "product.input.product-price.placeholder.label": "Enter Product Price", "product.input.product-unit.label": "Product Unit", "product.input.product-unit.placeholder.label": "Choose Product Unit", "product.input.sale-unit.label": "Sale Unit", "product.input.sale-unit.placeholder.label": "Choose Sale Unit", "product.input.purchase-unit.label": "Purchase Unit", "product.input.purchase-unit.placeholder.label": "Choose Purchase Unit", "product.input.stock-alert.label": "<PERSON>", "product.input.stock-alert.placeholder.label": "<PERSON><PERSON>", "product.input.order-tax.label": "Order Tax", "product.input.order-tax.placeholder.label": "Enter Order Tax", "product.input.order-tax.validate.label": "Please enter order tax", "product.input.order-tax.valid.validate.label": "The tax must not be greater than 100", "product.input.tax-type.label": "Tax Type", "tax-type.filter.exclusive.label": "Exclusive", "tax-type.filter.inclusive.label": "Inclusive", "product.input.tax-type.placeholder.label": "Choose Tax Type", "product.input.warehouse.placeholder.label": "Choose Warehouse", "product.input.multiple-image.label": "Multiple Image", "product.table.image.column.label": "Image", "product.table.price.column.label": "Price", "product.product-details.title": "Product Details", "product.product-details.code-product.label": "Product Code", "product.product-details.category.label": "Category", "product.product-details.cost.label": "Cost", "product.product-details.unit.label": "Unit", "product.product-details.tax.label": "Tax", "product.input.code.validate.label": "Please enter code", "product.input.product-category.validate.label": "Please select product category", "product.input.brand.validate.label": "Please select brand", "product.input.barcode-symbology.validate.label": "Please select barcode symbology", "product.input.product-cost.validate.label": "Please enter product cost", "product.input.product-price.validate.label": "Please enter product price", "product.input.product-unit.validate.label": "Please select product unit", "product.input.sale-unit.validate.label": "Please select sale unit", "product.input.purchase-unit.validate.label": "Please select purchase unit", "product.input.stock-alert.validate.label": "Please enter stock alert", "product.input.tax-type.validate.label": "Please select tax type", "product.input.warehouse.validate.label": "Please select warehouse", "product.success.create.message": "Product created successfully", "product.success.edit.message": "Product updated successfully", "product.success.delete.message": "Product deleted successfully", "product.image.success.upload.message": "Image uploaded successfully", "product.image.success.delete.message": "Image deleted successfully", "brands.title": "Brands", "brand.title": "Brand", "brand.create.title": "Create Brand", "brand.edit.title": "Edit Brand", "brand.input.code.label": "Code", "globally.input.change-logo.tooltip": "Change Logo", "globally.input.change-image.tooltip": "Change Image", "brand.table.brand-name.column.label": "Brand Name", "brand.table.product-count.column.label": "Product Count", "brand.input.name.valid.validate.label": "The name must not be greater than 50 characters", "brand.success.create.message": "Brand created successfully", "brand.success.edit.message": "Brand updated successfully", "brand.success.delete.message": "Brand deleted successfully", "product-categories.title": "Product Categories", "product-category.title": "Product Category", "product-category.create.title": "Create Product Category", "product-category.edit.title": "Edit Product Category", "product-category.success.create.message": "Product category created successfully", "product-category.success.edit.message": "Product category updated successfully", "product-category.success.delete.message": "Product category deleted successfully", "expense-categories.title": "Expense Categories", "expense-category.title": "Expense Category", "expense-category.create.title": "Create Expense Category", "expense-category.edit.title": "Edit Expense Category", "expense-category.success.create.message": "Expense category created successfully", "expense-category.success.edit.message": "Expense category updated successfully", "expense-category.success.delete.message": "Expense category deleted successfully", "expenses.title": "Expenses", "expense.title": "Expense", "expense.create.title": "Create Expense", "expense.edit.title": "Edit Expense", "expense.input.details.label": "Details", "expense.input.details.placeholder.label": "Enter Details", "expense.input.amount.label": "Amount", "expense.input.title.label": "Expense Title", "expense.input.title.validate.label": "Please enter expense title", "expense.input.title.placeholder.label": "Enter Expense Title", "expense.input.amount.placeholder.label": "Enter Amount", "expense.input.warehouse.placeholder.label": "Choose Warehouse", "expense.input.expense-category.placeholder.label": "Choose Expense Category", "expense.input.warehouse.validate.label": "Please select warehouse", "expense.input.expense-category.validate.label": "Please select expense category", "expense.input.amount.validate.label": "Please enter amount", "expense.success.create.message": "Expense created successfully", "expense.success.edit.message": "Expense updated successfully", "expense.success.delete.message": "Expense deleted successfully", "roles.title": "Roles", "roles.permissions.title": "Roles/Permissions", "role.title": "Role", "role.create.title": "Create Role", "role.edit.title": "Edit Role", "role.select.all-permission.label": "All Permissions", "role.input.permission.label": "Permissions", "role.input.name.validate.label": "Please enter name", "role.input.name.valid.validate.label": "The name must not be greater than 50 characters", "role.success.create.message": "Role created successfully", "role.success.edit.message": "Role updated successfully", "role.success.delete.message": "Role deleted successfully", "units.title": "Units", "unit.title": "Unit", "unit.create.title": "Create Unit", "unit.edit.title": "Edit Unit", "unit.modal.input.short-name.label": "Short Name", "unit.modal.input.short-name.placeholder.label": "Enter Short Name", "unit.modal.input.base-unit.label": "Base Unit", "unit.modal.input.base-unit.placeholder.label": "Choose Base Unit", "unit.modal.input.short-name.validate.label": "Please enter short name", "unit.modal.input.short-name.valid.validate.label": "The short name must not be greater than 50 characters", "unit.modal.input.base-unit.validate.label": "Please choose base unit", "unit.success.create.message": "Unit created successfully", "unit.success.edit.message": "Unit updated successfully", "unit.success.delete.message": "Unit deleted successfully", "currencies.title": "Currencies", "currency.title": "<PERSON><PERSON><PERSON><PERSON>", "currency.create.title": "Create C<PERSON><PERSON>cy", "currency.edit.title": "<PERSON>", "currency.modal.input.name.placeholder.label": "Enter Currency Name", "currency.modal.input.code.label": "Code", "currency.modal.input.code.placeholder.label": "Enter Currency Code", "currency.modal.input.symbol.label": "Symbol", "currency.modal.input.symbol.placeholder.label": "Enter Currency Symbol", "currency.modal.input.name.validate.label": "Please enter currency name", "currency.modal.input.code.validate.label": "Please enter currency code", "currency.modal.input.code.valid.validate.label": "The code must not be greater than 20 characters", "currency.modal.input.symbol.validate.label": "Please enter currency symbol", "currency.success.create.message": "Currency created successfully", "currency.success.edit.message": "Currency updated successfully", "currency.success.delete.message": "Currency deleted successfully", "purchases.title": "Purchases", "purchases.purchases-details.title": "Purchases Details", "purchase.title": "Purchase", "purchase.create.title": "Create Purchase", "purchase.edit.title": "<PERSON> Purchase", "purchase.select.warehouse.label": "Warehouse", "purchase.select.warehouse.placeholder.label": "Choose Warehouse", "purchase.select.supplier.label": "Supplier", "purchase.select.supplier.placeholder.label": "<PERSON><PERSON> Supplier", "purchase.select.warehouse.validate.label": "Please select warehouse", "purchase.select.supplier.validate.label": "Please select supplier", "purchase.select.status.label": "Status", "purchase.select.status.placeholder.label": "Choose <PERSON>", "purchase.order-item.table.label": "Order Items", "purchase.order-item.table.net-unit-cost.column.label": "Net Unit Cost", "purchase.order-item.table.stock.column.label": "Stock", "purchase.order-item.table.qty.column.label": "Qty", "purchase.order-item.table.discount.column.label": "Discount", "purchase.order-item.table.tax.column.label": "Tax", "purchase.order-item.table.sub-total.column.label": "Subtotal", "purchase.input.order-tax.label": "Order Tax", "purchase.input.shipping.label": "Shipping", "purchase.input.discount.validate.label": "Please enter discount", "purchase.input.order-tax.validate.label": "Please enter order tax", "purchase.input.shipping.validate.label": "Please enter shipping", "purchase.product-modal.select.discount-type.label": "Discount Type", "discount-type.filter.percentage.label": "Percentage", "discount-type.filter.fixed.label": "Fixed", "purchase.table.column.reference-code.label": "Reference Code", "purchase.grant-total.label": "Grand Total", "purchase.product-list.validate.message": "Please add product to list", "purchase.success.create.message": "Purchase created successfully", "purchase.success.edit.message": "Purchase updated successfully", "purchase.success.delete.message": "Purchase deleted successfully", "purchase.placeholder.notes.input": "Enter Note", "purchases.details.title": "Purchase Details", "purchase.detail.supplier.info": "Supplier Info", "purchase.detail.purchase.info": "Purchase Info", "purchases.return.title": "Purchases Returns", "purchase.return.create.title": "Create Purchase Return", "purchase.return.edit.title": "Edit Purchase Return", "purchase.return.success.create.message": "Purchase return created successfully", "purchase.return.success.edit.message": "Purchase return updated successfully", "purchase.return.success.delete.message": "Purchase return deleted successfully", "purchases.return.details.title": "Purchase Return Details", "settings.title": "Settings", "settings.system-settings.title": "System Settings", "settings.system-settings.select.default-currency.label": "<PERSON><PERSON><PERSON>", "settings.system-settings.select.default-currency.placeholder.label": "<PERSON><PERSON>", "settings.system-settings.input.default-email.label": "<PERSON><PERSON><PERSON>", "settings.system-settings.input.default-email.placeholder.label": "<PERSON><PERSON>", "settings.system-settings.select.default-language.label": "Default Language", "settings.system-settings.select.default-language.placeholder.label": "<PERSON><PERSON> De<PERSON>ult Language", "settings.system-settings.select.default-customer.label": "De<PERSON><PERSON> Customer", "settings.system-settings.select.default-customer.placeholder.label": "<PERSON><PERSON> Customer", "settings.system-settings.select.default-warehouse.label": "Default Warehouse", "settings.system-settings.select.default-warehouse.placeholder.label": "<PERSON>ose Default Warehouse", "settings.system-settings.input.change-logo.label": "Change Logo", "settings.system-settings.input.company-name.label": "Company Name", "settings.system-settings.input.company-name.placeholder.label": "Enter Company Name", "settings.system-settings.input.company-phone.label": "Company Phone", "settings.system-settings.input.company-phone.placeholder.label": "Enter Company Phone", "settings.system-settings.input.developed-by.label": "Developed By", "settings.system-settings.input.developed-by.placeholder.label": "En<PERSON> Developed By", "settings.system-settings.input.footer.label": "Footer", "settings.system-settings.input.footer.placeholder.label": "<PERSON><PERSON>", "settings.payment-gateway.title": "Payment Gateway", "settings.payment-gateway.input.stripe-key.label": "STRIPE_KEY", "settings.payment-gateway.input.stripe-key.placeholder.label": "Please leave this field blank if you haven't changed it", "settings.payment-gateway.input.stripe-secret.label": "STRIPE_SECRET", "settings.payment-gateway.switch-btn.label": "Delete Stripe API keys", "settings.sms-configuration.title": "SMS Configuration", "settings.sms-configuration.select.sms-gateway.label": "SMS Gateway", "settings.sms-configuration.select.sms-gateway.placeholder.label": "Choose SMS Gateway", "settings.sms-configuration.input.twilio-sid.label": "TWILIO_SID", "settings.sms-configuration.input.twilio-token.label": "TWILIO_TOKEN", "settings.sms-configuration.select.twilio-from.label": "TWILIO_FROM", "settings.sms-configuration.select.twilio-from.placeholder.label": "Enter TWILIO FROM", "settings.sms-configuration.input.twilio-sid.placeholder.label": "Enter TWILIO SID", "settings.smtp-configuration.title": "SMTP Configuration", "settings.smtp-configuration.input.host.label": "HOST", "settings.smtp-configuration.input.port.label": "PORT", "settings.smtp-configuration.input.username.label": "Username", "settings.smtp-configuration.input.password.label": "Password", "settings.smtp-configuration.input.encryption.label": "Encryption", "settings.clear-cache.title": "<PERSON>ache", "settings.system-settings.select.default-currency.validate.label": "Please select currency", "settings.system-settings.input.company-name.validate.label": "Please enter company name", "settings.system-settings.input.company-phone.validate.label": "Please enter company phone", "settings.system-settings.input.developed-by.validate.label": "Please enter developed by", "settings.system-settings.input.footer.validate.label": "Please enter city", "settings.system-settings.select.default-language.validate.label": "Please select language", "settings.system-settings.select.default-customer.validate.label": "Please select customer", "settings.system-settings.select.default-warehouse.validate.label": "Please select warehouse", "settings.system-settings.select.address.validate.label": "Please enter address", "settings.system-settings.select.address.valid.validate.label": "The address must not be greater than 150 characters", "settings.sms-configuration.select.sms-gateway.validate.label": "Please select sms gateway", "settings.sms-configuration.input.twilio-sid.validate.label": "Please enter sid", "settings.sms-configuration.input.twilio-token.validate.label": "Please enter token", "settings.sms-configuration.select.twilio-from.validate.label": "Please enter twillo from", "settings.smtp-configuration.input.host.validate.label": "Please enter smtp host", "settings.smtp-configuration.input.port.validate.label": "Please enter smtp port", "settings.smtp-configuration.input.username.validate.label": "Please enter username", "settings.smtp-configuration.input.password.validate.label": "Please enter password", "settings.smtp-configuration.input.encryption.validate.label": "Please enter encryption", "settings.success.edit.message": "Setting updated successfully", "update-profile.input.full-name.label": "Full Name", "update-profile.title": "Profile Details", "update-profile.tab.title": "Update Profile", "update-profile.success.update.message": "Profile updated successfully", "change-password.input.current.label": "Current Password", "change-password.input.new.label": "New Password", "change-password.input.confirm.label": "Confirm Password", "change-password.input.current.placeholder.label": "Enter Current Password", "change-password.input.new.placeholder.label": "Enter New Password", "change-password.input.confirm.placeholder.label": "Enter Confirm Password", "change-password.input.current.validate.label": "Enter current password", "change-password.input.new.validate.label": "Enter new password", "change-password.input.confirm.validate.label": "Enter confirm password", "change-password.input.confirm.valid.validate.label": "The password confirmation does not match", "login-form.title": "Sign In", "login-form.login-btn.label": "<PERSON><PERSON>", "login-form.forgot-password.label": "Forgot Password ?", "forgot-password-form.reset-link-btn.label": "Send Password Reset Link", "forgot-password-form.success.reset-link.label": "We have emailed your password reset link!", "login.success.message": "Logged in successfully.", "logout.success.message": "<PERSON><PERSON><PERSON> successfully.", "change-language.update.success.message": "Language updated successfully", "reset-password.title": "Reset Password", "reset-password.password.validate.label": "The confirm password and password must match", "reset-password.success.update.message": "Your password has been reset!", "sales.title": "Sales", "sale.title": "Sale", "sale.create.title": "Create Sale", "sale.edit.title": "Edit Sale", "sale.select.customer.label": "Customer", "sale.select.customer.placeholder.label": "<PERSON>ose Customer", "sale.select.customer.validate.label": "Please select customer", "sale.select.payment-status.placeholder": "Choose Payment Status", "sale.order-item.table.net-unit-price.column.label": "Net Unit Price", "sale.product.table.no-data.label": "No Data Available", "sale.success.create.message": "Sale created successfully", "sale.success.edit.message": "Sale updated successfully", "sale.success.delete.message": "Sale deleted successfully", "sale.details.title": "Sale Details", "sale.detail.customer.info": "Customer Info", "sale.detail.invoice.info": "Invoice Info", "sales-return.title": "Sales Returns", "sale-return.title": "Sale Return", "sale-return.create.title": "Create Sale Return", "sale-return.edit.title": "Edit Sale Return", "sale-return.success.create.message": "Sale Return created successfully", "sale-return.success.edit.message": "Sale Return updated successfully", "sale-return.success.delete.message": "Sale Return deleted successfully", "sale-return.details.title": "Sale Return Details", "date-picker.filter.today.label": "Today", "date-picker.filter.this-week.label": "This Week", "date-picker.filter.last-week.label": "Last Week", "date-picker.filter.this-month.label": "This Month", "date-picker.filter.last-month.label": "Last Month", "date-picker.filter.Custom-Range.label": "Custom Range", "date-picker.filter.reset.label": "Reset", "date-picker.filter.apply.label": "Apply", "date-picker.filter.placeholder.label": "Select Date", "bar.title": "Bar", "line.title": "Line", "filter.label": "Filter", "reports.title": "Reports", "warehouse.reports.title": "Warehouse Reports", "sale.reports.title": "Sale Reports", "stock.reports.title": "Stock Reports", "purchase.reports.title": "Purchase Reports", "top-selling-product.reports.title": "Top Selling Products Reports", "globally.react-table.column.code.label": "Code", "print.barcode.title": "Print Barcode", "paper.size.title": "Paper Size", "paper.size.placeholder.label": "Choose Paper size", "globally.paper.size.validate.label": "Please select paper size", "print.validate.label": "Please update the barcode to print", "current.stock.label": "Current Stock", "stock.report.details.title": "Stock Report Details", "print.title": "Print", "update.title": "Update", "preview.title": "Preview", "toast.successful.title": "Successful", "toast.error.title": "Something Went wrong!", "unit.filter.all.label": "All", "unit.filter.piece.label": "Piece", "unit.filter.meter.label": "<PERSON>er", "unit.filter.kilogram.label": "Kilogram", "status.filter.received.label": "Received", "status.filter.pending.label": "Pending", "status.filter.ordered.label": "Ordered", "payment-status.filter.paid.label": "Paid", "payment-status.filter.unpaid.label": "Unpaid", "excel.btn.label": "EXCEL", "cash.label": "Cash", "no-option.label": "No options", "select.payment-type.label": "Payment Type", "payment-type.filter.cheque.label": "Cheque", "payment-type.filter.bank-transfer.label": "Bank Transfer", "payment-type.filter.other.label": "Other", "paying-amount-title": "Paying Amount", "create-payment-title": "Create Payment", "reference-placeholder-label": "Enter Reference", "input-Amount-to-pay-title": "Amount To Pay", "paying-amount-validate-label": "Paying amount should be less than or equal to Amount to pay", "edit-payment-title": "Edit Payment", "no-product-found.label": "No Products Found", "sale.select.payment-type.placeholder": "Choose Payment Type", "globally.payment.type.validate.label": "Please select payment type", "pos.payment.amount.exceeds.total.error": "Payment amount cannot exceed the total amount", "pos.payment.total.exceeds.grand.total.error": "Total payment amount exceeds the grand total", "globally.payment.details.validate.label": "Please check payment details for errors", "product.quantity.alert.reports.title": "Product Quantity Alerts", "globally-saving-btn-label": "Saving...", "payment-status.filter.partial.label": "Partial", "dashboard.recentSales.total-product.label": "Total Products", "adjustments.title": "Adjustments", "adjustments.create.title": "Create Adjustment", "adjustments.edit.title": "Edit Adjustment", "adjustments.detail.title": "Adjustment Details", "Adjustment.success.create.message": "Adjustment created successfully", "Adjustment.success.edit.message": "Adjustment updated successfully", "Adjustment.success.delete.message": "Adjustment deleted successfully", "login-form.go-to-sign-in.label": "Back To Sign In", "pos-product.title": "PRODUCT", "pos-qty.title": "QTY", "pos-price.title": "PRICE", "pos-sub-total.title": "SUB TOTAL", "pos-total-qty.title": "Total QTY", "pos-total.title": "Total", "pos-pay-now.btn": "Pay Now", "pos-globally.search.field.label": "Scan/Search Product by Code Name", "pos-make-Payment.title": "Make Payment", "pos-received-amount.title": "Received Amount", "pos-total-amount.title": "Total Amount", "pos-sale.detail.invoice.info": "Invoice", "pos-sale.detail.Phone.info": "Phone", "pos-sale.detail.Paid-bt.title": "<PERSON><PERSON>", "pos-close-btn.title": "Close", "pos-item.print.invoice.title": "<PERSON><PERSON>", "pos-all.categories.label": "All Categories", "pos-all.brands.label": "All Brands", "pos-no-product-available.label": "No Products Available", "pos-sale.select.discount-type.placeholder": "Choose Discount Type", "pos-sale.select.sale-unit-type.placeholder": "Choose Sale Unit Type", "pos-thank.you-slip.invoice": "Thank You For Shopping With Us. Please visit again", "pos.payment.success.message": "Payment successfully done", "transfers.title": "Transfers", "transfer.title": "Transfer", "transfer.create.title": "Create Transfer", "transfer.edit.title": "Edit Transfer", "transfer.from-warehouse.title": "From Warehouse", "transfer.to-warehouse.title": "To Warehouse", "pos.cash-payment.product-error.message": "Please add product to cart", "pos.cash-payment.quantity-error.message": "Please add quantity of product", "pos.cash-payment.tax-error.message": "Please enter tax value between 0 to 100", "pos.cash-payment.total-amount-error.message": "Discount amount should not be greater than total", "pos.subtotal.small.title": "Sub Total", "settings.system-settings.select.default-version-footer.placeholder.label": "Show version number in footer", "settings.system-settings.select.logo.placeholder.label": "Show logo in payment slip", "settings.system-settings.select.appname-sidebar.placeholder.label": "Show app name in sidebar", "pos.cash-payment.sub-total-amount-error.message": "Shipping amount should not be greater than sub total", "product.import.title": "Import Products", "globally.sample.download.label": "Download Sample", "product-code.import.required-highlight.message": "code must be not exist already", "product-unit.import.required-highlight.message": "unit must already be created Please use full name of unit", "globally.optional-input.validate.label": "Field optional", "reset.title": "Reset", "reset.yes.title": "Yes, Reset", "reset.modal.msg": "Are you sure want to reset", "dashboard.widget.today-total-purchases.label": "Today Total Purchases", "dashboard.widget.today-payment-received.label": "Today Total Received(Sales)", "dashboard.widget.today-total-sales.label": "Today Total Sales", "dashboard.widget.today-total-expense.label": "Today Total Expense", "globally.file.validate.label": "Please select file", "globally.csv-file.validate.label": "Please select csv file", "file.success.upload.message": "File uploaded successfully", "transfer.success.create.message": "Transfer created successfully.", "transfer.success.edit.message": "Transfer updated successfully.", "transfer.success.delete.message": "Transfer deleted successfully.", "transfer.select.warehouse.validate.message": "You can not transfer stock in same warehouse", "status.filter.complated.label": "Completed", "status.filter.sent.label": "<PERSON><PERSON>", "settings.prefixes-settings.input.purchases.placeholder.label": "PU", "settings.prefixes-settings.input.purchases-return.placeholder.label": "PR", "settings.prefixes-settings.input.sales.placeholder.label": "SL", "settings.prefixes-settings.input.salse-return.placeholder.label": "SR", "settings.prefixes-settings.input.expense.placeholder.label": "EX", "settings.prefixes-settings.input.purchases.validate.label": "Please enter purchases", "settings.prefixes-settings.input.purchases-return.validate.label": "Please enter purchases return", "settings.prefixes-settings.input.sales.validate.label": "Please enter sales", "settings.prefixes-settings.input.salse-return.validate.label": "Please enter sales return", "settings.prefixes-settings.input.expense.validate.label": "Please enter expense", "transfer.details.title": "Transfer Details", "setting.state.lable": "State", "setting.postCode.lable": "Postal Code", "settings.system-settings.select.state.validate.label": "Please select state", "settings.system-settings.select.country.validate.label": "Please select country", "settings.system-settings.select.postcode.validate.label": "Please enter postal code", "side-menu.empty.message": "No matching records found", "product.export.title": "Export Products", "globally.input.content.label": "Content", "email-template.edit.title": "Edit <PERSON><PERSON>late", "email-template.title": "Email Templates", "email-template.success.edit.message": "Email template updated successfully.", "purchases.total.amount.title": "Purchases Total Amount", "purchases-return.total.amount.title": "Purchases Return Total Amount", "supplier.report.details.title": "Supplier Report Details", "supplier.report.title": "Suppliers Reports", "prefix.title": "Prefixes", "quotations.title": "Quotations", "create-quotation.title": "Create Quotation", "edit-quotation.title": "Edit Quotation", "details-quotations.title": "Quotation Details", "quotation.success.create.message": "Quotation created successfully", "quotation.success.edit.message": "Quotation updated successfully", "quotation.success.delete.message": "Quotation deleted successfully", "settings.system-settings.select.date-format.label": "Date Format", "quotation.title": "Quotation", "converted.status.label": "Converted", "mail-settings.sender-name.title": "Sender Name", "mail-settings.title": "Mail Settings", "mail-settings.success.edit.message": "Mail setting updated successfully", "settings.system-settings.select.postcode.validate.length.label": "Postal Code length should not be more than 8", "best-customer.report.title": "Best Customers", "sms-template.edit.title": "Edit SMS Template", "sms-template.title": "SMS Templates", "sms-template.success.edit.message": "SMS template updated successfully.", "sms-content-variables.title": "SMS CONTENT VARIABLES", "email-content-variables.title": "EMAIL CONTENT VARIABLES", "sms-content-text.error.message": "You've reached the maximum allowed characters 300.", "sms-content.error.message": "content must be required", "profit-loss.reports.title": "Profit & Loss", "global.revenue.title": "Revenue", "global.gross-profit.title": "Gross Profit", "global.payment-received.title": "Payments Received", "global.payment-sent.title": "Payment Sent", "global.net-payment.title": "Payments Net", "customer.report.details.title": "Customer Report Details", "customer.report.title": "Customer Reports", "sale.payment.report.title": "Sale Payment", "sale.total.amount.title": "Sale Total Amount", "sale-return.total.amount.title": "Sale Return Total Amount", "sale-Due.total.amount.title": "Total Due Amount", "sale-paid.total.amount.title": "Total Paid Amount", "sale-reference.title": "Sale Reference", "quotation.detail.invoice.info": "Quotation Info", "pepole.title": "Peoples", "more-report.option.title": "More", "currency.icon.right.side.lable": "Currency icon Right side", "pos.register-details.sell.title": "<PERSON>ll", "total.sales.title": "Total Sales", "email.status.edit.success.message": "Email status updated successfully.", "sms.status.edit.success.message": "SMS status updated successfully.", "sms-api.title": "SMS API", "key.lable": "Key", "key.value.lable": "Key Value", "url.lable": "URL", "mobile.key.lable": "Mobile Key", "message.key.lable": "Message Key", "sms.status.lable": "SMS Status", "active.status.lable": "Active", "in-active.status.lable": "In-Active", "sms.api.update.success.message": "SMS API Updated Successfully.", "sale-return.product-qty.validate.message": "Quantity return is greater than quantity sold", "template.title": "Templates", "pos.product-quantity-error.message": "No more quantity available.", "product-list.lable": "Product List", "register.details.title": "Register Details", "register.total-sales.label": "Total Sales", "register.total-refund.title": "Total Refund", "register.total-payment.title": "Total Payment", "register.product.sold.title": "Details of products sold", "register.product.sold.by.brand.title": "Details of products sold (By Brand)", "print-barcode.show-company.label": "Show Store Name", "print-barcode.show-product-name.label": "Show Product Name", "print-barcode.show-price.label": "Show Price", "product.input.quantity-limitation.label": "Quantity Limitation", "product.input.quantity-limitation.placeholder": "Enter Quantity Limitation", "pos.hold-list-btn.title": "Hold", "create.hold-list.warning.message": "Hold Invoice ? Same Reference will replace the old list if exist!!", "create-modal.yes.ok-btn": "Yes, Ok", "hold-list.reference-number.placeholder": "Please Enter Reference Number!", "hold-list-id.table.column.label": "ID", "hold-list-ref-id.table.column.label": "Ref.ID", "hold-list.details.title": "Hold List", "confirm-modal.msg": "Are you sure?", "hold-list.success.create.message": "Hold-list created successfully", "hold-list.success.delete.message": "Hold-list deleted successfully", "report-all.warehouse.label": "All Warehouse", "setting.mail-mailer.lable": "MAIL_MAILER", "setting.mail-host.lable": "MAIL_HOST", "setting.mail-port.lable": "MAIL_PORT", "setting.mail-user-name.lable": "MAIL_USERNAME", "setting.mail-password.lable": "MAIL_PASSWORD", "setting.mail-encryption.lable": "MAIL_ENCRYPTION", "sale.payment.create.success": "Sale payment created successfully", "sale.payment.edit.success": "Sale payment updated successfully", "hold-list.reference-code.error": "The reference code field is required.", "settings.clear-cache.success.message": "cache clear successfully", "product.product-in-stock.label": "In stock", "product-items.label": "Items", "Payload.key.lable": "Payload", "base-units.title": "Base Units", "base-unit.create.title": "Create Base Unit", "base-unit.edit.title": "Edit Base Unit", "base-unit.title": "Base Unit", "base-unit.success.create.message": "Base Unit created successfully", "base-unit.success.edit.message": "Base Unit updated successfully", "base-unit.success.delete.message": "Base Unit deleted successfully", "DOB.input.label": "DOB", "purchase.product.quantity.validate.label": "Please enter product quantity", "languages.title": "Languages", "react-data-table.translation.column.label": "Translation", "react-data-table.iso-date.column.label": "ISO Code", "globally.input.iso-code.validate.label": "Please enter ISO Code", "globally.input.iso-code.character.validate.label": "ISO Code length should be equal to 2", "translation.manager.title": "Translation Manager", "language.updated.success.message": "Language updated successfully", "language.deleted.success.message": "Language deleted successfully", "language.edit.success.message": "Language edited successfully", "language.save.success.message": "Language created successfully", "language.enabled.success.message": "Language enabled successfully", "language.disabled.success.message": "Language disabled successfully", "language.current-language-disable.error.message": "You cannot disable your currently selected language", "header.profile-menu.change-language.label": "Change Language", "language.title": "Language", "pos-paying-amount.title": "Paying Amount", "pos.change-return.label": "Change Return", "language.create.title": "Create Language", "language.edit.title": "Edit Language", "purchase.less.recieving.ammout.error": "Receiving Amount is Smaller than Grand Total.", "add-stock.title": "Add Stock", "product-quantity.add.title": "Add Product Quantity", "edit-translation.title": "Edit Translation", "globally.input.cash-in-hand.label": "Cash In Hand", "globally.close-register.title": "Close Register", "register.closed.successfully.message": "Register closed successfully.", "register.entry.added.successfully.message": "Register entry added successfully.", "globally.total-cash.label": "Total Cash", "globally.input.note.label": "Note", "globally.input.note.placeholder.label": "Enter Note", "register.report.title": "Register Report", "user-details.table.opened-on.row.label": "Opened On", "user-details.table.closde-on.row.label": "Closed On", "globally.input.cash-in-hand-while-closing.label": "Cash In Hand While Closing", "pos.cclose-register.enter-total-cash.message": "Please add total cash.", "register.is.still.open.message": "Register is still open !!!", "Are.you.sure.you.want.to.go.to.dashboard.message": "Are you sure you want to go to Dashboard ?", "product.quantity.title": "Product Quantity", "pos.this.product.out.of.stock.message": "This product is out of stock", "pos.quantity.exceeds.quantity.available.in.stock.message": "Quantity exceeds quantity available in stock", "yes.modal.title": "Yes", "no.modal.title": "No", "pdf.btn.label": "PDF", "select.user.label": "Select User", "suppliers.import.title": "Import Suppliers", "customers.import.title": "Import Customers", "products.type.single-type.label": "Single", "product.type.label": "Product Type", "product.type.placeholder.label": "Choose Product Type", "product.type.input.validation.error": "Please select product type", "products.warehouse.title": "Product Warehouse", "barcode-symbol-uppercase-validation-message": "Please enter only uppercase letters and numbers for Code 39 Barcode Symbology.", "sale.product-qty.limit.validate.message": "You can't buy more than limit quantity", "receipt-settings.title": "Receipt Settings", "receipt-settings.show-warehouse.label": "Show Warehouse", "receipt-settings.show-email.label": "Show Email", "receipt-settings.show-address.label": "Show Address", "receipt-settings.show-customer.label": "Show Customer", "receipt-settings.show-phone.label": "Show Phone", "receipt-settings.show-discount-shipping.label": "Show Discount & Shipping", "receipt-settings.success.edit.message": "Receipt setting updated successfully", "receipt-settigns.input.note.validate.label": "Please enter note", "receipt-settings.show-barcode.label": "Show barcode in receipt", "receipt-settings.show-note.label": "Show note", "globally.submit-and-print-button": "Submit & Print", "receipt-settings.show-product-code.label": "Show Product Code", "globally.footer.label": "All Rights Reserved", "logout.confirmation.label": "Are you sure you want to Logout?", "addition.title": "Addition", "subtraction.title": "Subtraction", "print-custom-barcode.title": "Print Custom Barcode", "product.sku.label": "SKU/Barcode", "add.stock.while.product.creation.title": "Add stock while product creation", "store.title": "Stores", "create.store.title": "Create Store", "edit.store.title": "Edit Store", "store.name.title": "Store Name", "store.name.placeholder.title": "Enter store name", "store.name.validate.label": "Please enter store name", "store.success.create.message": "Store created successfully", "store.success.edit.message": "Store updated successfully", "store.success.delete.message": "Store deleted successfully", "store.changed.message": "Store changed successfully", "store.header.name.title": "Store", "select.all.store.title": "Select all store", "store.field.must.required.validate": "Store field must be required", "store.assigned.title": "Stores Assigned", "no.store.title": "No store info", "paid.amount.title": "Paid amount", "taxes.title": "Taxes", "tax.title": "Tax", "add.tax.title": "Add Tax", "edit.tax.title": "Edit Tax", "tax.name.title": "Tax name", "tax.name.placeholder.title": "Enter tax name", "tax.name.validate.title": "Please enter tax name", "tax.value.title": "Tax value", "tax.value.placeholder.title": "Enter tax value", "tax.value.validate.title": "Please enter tax value", "tax.deleted.success.message": "Tax deleted successfully", "tax.edit.success.message": "Tax edited successfully", "tax.save.success.message": "Tax created successfully", "tax.name.unique.validate.title": "Tax name already exists", "tax.value.unique.validate.title": "Tax value already exists", "tax.show.on.receipt.pdf.title": "Show on receipt/PDF", "pos.settings.title": "POS settings", "enable.pos.sound.title": "Enable POS click sound", "show.out.of.stock.product.in.pos": "Show out of stock products in POS", "pos.sound.title": "POS Sound", "upload.audio.title": "Upload Audio", "pos.audio.required": "Please upload audio", "date.of.birth.title": "Date of birth", "customer.details.title": "Customer Details", "pos.audio.length.tooltip.title": "Audio length should be less than 3 seconds.", "select.date.of.birth": "Select your date of birth", "item.deleted.success.message": "Item deleted successfully", "payment.methods.title": "Payment Methods", "payment.method.title": "Payment Method", "create.payment.methods.title": "Add Payment Method", "edit.payment.methods.title": "Edit Payment Method", "payment.method.save.success.message": "Payment method created successfully", "payment.method.edit.success.message": "Payment method edited successfully", "payment.method.deleted.success.message": "Payment method deleted successfully", "payment.method.name.unique.validate.title": "Payment method name already exists", "show.tax.title": "Show Tax", "expiry.date.title": "Expiry date", "expiry.date.placeholder.title": "Enter expiry date", "dual.screen.settings.title": "Dual Screen Settings", "dual.screen.display.header.title": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "dual.screen.display.header.placeholder.title": "<PERSON><PERSON> Di<PERSON>lay Header", "please.enter.display.header.title": "Please Enter Di<PERSON>lay Header", "carousel.image.title": "Carousel Images", "validation.you.can.upload.maximum.images": "You can upload maximum 5 images", "upload.maximum.images": "Upload a maximum 5 images.", "no.customer.selected.title": "No Customer Selected", "send.test.email.title": "Send Test Email", "send.test.email.success.message": "Test email sent successfully.", "globally.receipt.download.label": "Download Receipt", "load.more.title": "Load More", "pos.edit.sale.title": "Are you sure you want edit this sale?", "sale.payment.total-exceed.validate.message": "Total payment amount must not exceed the grand total.", "globally.input.width.label": "<PERSON><PERSON><PERSON>", "globally.input.height.label": "Height", "globally.input.width.validate.label": "Please enter width", "globally.input.height.validate.label": "Please enter height"}