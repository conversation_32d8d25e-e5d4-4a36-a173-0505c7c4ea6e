{"dashboard.title": "仪表板", "header.pos.title": "售貨點", "header.profile-menu.profile.label": "个人资料", "header.profile-menu.change-password.label": "修改密码", "header.profile-menu.logout.label": "注销", "product.categories.title": "产品类别", "expense.categories.title": "费用类别", "dashboard.salesReturn.title": "销售退货", "dashboard.top-customers.title": "前 5 名客户", "dashboard.purchaseReturn.title": "购买退货", "dashboard.ThisWeekSales&Purchases.title": "本周销售和采购", "dashboard.TopSellingProducts.title": "畅销产品", "dashboard.stockAlert.title": "库存警报", "dashboard.recentSales.title": "近期销售", "dashboard.PaymentSentReceived.title": "付款已发送和接收", "dashboard.stockAlert.code.label": "代码", "dashboard.stockAlert.product.label": "产品", "dashboard.stockAlert.warehouse.label": "仓库", "dashboard.stockAlert.quantity.label": "数量", "dashboard.stockAlert.alertQuantity.label": "警报数量", "dashboard.grantTotal.label": "总计", "dashboard.recentSales.reference.label": "参考", "dashboard.recentSales.customer.label": "客户", "dashboard.recentSales.status.label": "状态", "dashboard.recentSales.paid.label": "付费", "dashboard.recentSales.due.label": "到期", "dashboard.recentSales.paymentStatus.label": "付款状态", "dataTable.searchBar.placeholder.label": "搜索", "settings.select.language.label": "语言", "settings.select.language.placeholder": "选择语言", "users.title": "用户", "user.create.title": "创建用户", "user.edit.title": "编辑用户", "user.input.first-name.label": "名字", "user.input.last-name.label": "姓氏", "user.input.email.label": "电子邮件", "user.input.phone-number.label": "电话号码", "user.input.password.label": "密码", "user.input.confirm-password.label": "确认密码", "user.input.role.label": "角色", "user.input.first-name.placeholder.label": "输入名字", "user.input.last-name.placeholder.label": "输入姓氏", "user.input.email.placeholder.label": "输入邮箱", "user.input.phone-number.placeholder.label": "输入电话号码", "user.input.password.placeholder.label": "输入密码", "user.input.confirm-password.placeholder.label": "输入确认密码", "user.input.role.placeholder.label": "选择角色", "users.table.user.column.title": "用户", "users.table.phone-number.column.title": "电话号码", "users.table.role.column.title": "角色", "users.table.date.column.title": "创建日期", "user-details.title": "用户详细信息", "user-details.table.created-on.row.label": "创建时间", "user-details.table.title": "概览", "user.input.first-name.validate.label": "请输入名字", "user.input.last-name.validate.label": "请输入姓氏", "user.input.email.validate.label": "请输入邮箱地址", "user.input.email.valid.validate.label": "请输入有效的电子邮件地址", "user.input.phone-number.validate.label": "请输入电话号码", "user.input.password.validate.label": "请输入密码", "user.input.confirm-password.validate.label": "请输入确认密码", "user.input.role.validate.label": "请选择角色", "user.success.create.message": "用户创建成功", "user.success.edit.message": "用户更新成功", "user.success.delete.message": "用户删除成功", "react-data-table.searchbar.placeholder": "搜索", "react-data-table.action.column.label": "动作", "react-data-table.date.column.label": "日期", "react-data-table.no-record-found.label": "没有要显示的记录", "react-data-table.records-per-page.label": "每页记录数", "delete-modal.title": "删除！", "delete-modal.msg": "你确定要删除这个吗", "delete-modal.yes-btn": "是的，删除！", "delete-modal.no-btn": "否，取消", "suppliers.title": "供应商", "supplier.title": "供应商", "supplier.create.title": "创建供应商", "supplier.edit.title": "编辑供应商", "supplier.table.name.column.title": "名称", "supplier.table.phone-number.column.title": "电话号码", "supplier.table.email.column.title": "电子邮件", "supplier.table.address.column.title": "地址", "supplier.success.create.message": "供应商创建成功", "supplier.success.edit.message": "供应商更新成功", "supplier.success.delete.message": "供应商删除成功", "globally.input.name.label": "名称", "globally.react-table.column.created-date.label": "创建时间", "globally.react-table.column.payment-type.label": "支付类型", "globally.input.name.placeholder.label": "输入名称", "globally.input.email.label": "电子邮件", "globally.input.email.placeholder.label": "输入邮箱", "globally.input.phone-number.label": "电话号码", "globally.input.phone-number.placeholder.label": "输入电话号码", "globally.input.country.label": "国家", "globally.input.country.placeholder.label": "输入国家", "globally.input.city.label": "城市", "globally.input.city.placeholder.label": "输入城市", "globally.input.address.label": "地址", "globally.input.address.placeholder.label": "输入地址", "globally.input.notes.label": "注释", "globally.input.notes.placeholder.label": "输入注释", "globally.loading.label": "请稍候...", "globally.input.name.validate.label": "请输入姓名", "globally.input.email.validate.label": "请输入电子邮件地址", "globally.input.email.valid.validate.label": "请输入有效的电子邮件地址", "globally.input.country.validate.label": "请输入国家", "globally.input.city.validate.label": "请输入城市", "globally.input.phone-number.validate.label": "请输入电话号码", "globally.input.address.validate.label": "请输入地址", "globally.input.notes.validate.label": "注释不得超过 100 个字符", "globally.require-input.validate.label": "此字段为必填项", "globally.date.validate.label": "请输入日期", "globally.tax-length.validate.label": "税款不得大于 100", "globally.discount-length.validate.label": "折扣不能大于 100", "globally.discount-cost-length.validate.label": "折扣不得大于产品成本", "globally.discount-price-length.validate.label": "折扣不得大于商品价格", "globally.type.label": "类型", "globally.back-btn": "返回", "globally.save-btn": "保存", "globally.cancel-btn": "取消", "globally.edit-btn": "编辑", "globally.submit-btn": "提交", "globally.edit.tooltip.label": "编辑", "globally.delete.tooltip.label": "删除", "globally.view.tooltip.label": "视图", "globally.pdf.download.label": "下载 PDF", "globally.product-quantity.validate.message": "请添加产品数量", "globally.product-already-added.validate.message": "该产品已添加", "globally.detail.company.info": "公司信息", "globally.detail.reference": "参考", "globally.detail.status": "状态", "globally.detail.warehouse": "仓库", "globally.detail.payment.status": "支付状态", "globally.detail.payment.details": "付款详情", "globally.detail.order.summary": "订单汇总", "globally.detail.product": "产品", "globally.detail.net-unit-cost": "净单位成本", "globally.detail.net-unit-price": "净单价", "globally.detail.quantity": "数量", "globally.detail.unit-cost": "单位成本", "globally.detail.unit-price": "单价", "globally.detail.discount": "折扣", "globally.detail.tax": "税", "globally.show.payment.label": "显示付款", "globally.detail.subtotal": "小计", "globally.detail.order.tax": "订单税", "globally.detail.shipping": "运费", "globally.detail.grand.total": "总计", "globally.detail.paid": "付费", "globally.search.field.label": "按代号搜索产品", "globally.detail.due": "到期", "globally.status.validate.label": "请选择状态", "globally.payment.status.validate.label": "请选择支付状态", "customers.title": "客户", "customer.title": "客户", "customer.create.title": "创建客户", "customer.edit.title": "编辑客户", "customer.success.create.message": "客户创建成功", "customer.success.edit.message": "客户更新成功", "customer.success.delete.message": "客户删除成功", "warehouse.title": "仓库", "warehouse.create.title": "创建仓库", "warehouse.edit.title": "编辑仓库", "warehouse.details.title": "仓库详情", "warehouse.input.zip-code.label": "邮政编码", "warehouse.input.zip-code.placeholder.label": "输入邮政编码", "warehouse.input.zip-code.validate.label": "请输入邮政编码", "warehouse.input.zip-code.valid.validate.label": "请输入有效的邮政编码", "warehouse.success.create.message": "仓库创建成功", "warehouse.success.edit.message": "仓库更新成功", "warehouse.success.delete.message": "仓库删除成功", "products.title": "产品", "product.title": "产品", "product.create.title": "创建产品", "product.edit.title": "编辑产品", "product.input.code.label": "代码", "product.input.code.placeholder.label": "输入代码", "product.input.product-category.label": "产品类别", "product.input.product-category.placeholder.label": "选择产品类别", "product.input.brand.label": "品牌", "product.input.brand.placeholder.label": "选择品牌", "product.input.barcode-symbology.label": "条码符号", "product.input.barcode-symbology.placeholder.label": "选择条形码符号", "product.input.product-cost.label": "产品成本", "product.input.product-cost.placeholder.label": "输入产品成本", "product.input.product-price.label": "产品价格", "product.input.product-price.placeholder.label": "输入产品价格", "product.input.product-unit.label": "产品单元", "product.input.product-unit.placeholder.label": "选择产品单元", "product.input.sale-unit.label": "销售单位", "product.input.sale-unit.placeholder.label": "选择销售单位", "product.input.purchase-unit.label": "采购单位", "product.input.purchase-unit.placeholder.label": "选择购买单位", "product.input.stock-alert.label": "库存警报", "product.input.stock-alert.placeholder.label": "输入库存警报", "product.input.order-tax.label": "订单税", "product.input.order-tax.placeholder.label": "输入订单税", "product.input.order-tax.validate.label": "请输入订单税", "product.input.order-tax.valid.validate.label": "税款不得大于100", "product.input.tax-type.label": "税种", "tax-type.filter.exclusive.label": "独家", "tax-type.filter.inclusive.label": "包含", "product.input.tax-type.placeholder.label": "选择税种", "product.input.warehouse.placeholder.label": "选择仓库", "product.input.multiple-image.label": "多个图像", "product.table.image.column.label": "图片", "product.table.price.column.label": "价格", "product.product-details.title": "产品详情", "product.product-details.code-product.label": "產品代碼", "product.product-details.category.label": "类别", "product.product-details.cost.label": "成本", "product.product-details.unit.label": "单位", "product.product-details.tax.label": "税", "product.input.code.validate.label": "请输入代码", "product.input.product-category.validate.label": "请选择产品类别", "product.input.brand.validate.label": "请选择品牌", "product.input.barcode-symbology.validate.label": "请选择条形码符号", "product.input.product-cost.validate.label": "请输入产品成本", "product.input.product-price.validate.label": "请输入产品价格", "product.input.product-unit.validate.label": "请选择产品单位", "product.input.sale-unit.validate.label": "请选择销售单位", "product.input.purchase-unit.validate.label": "请选择购买单位", "product.input.stock-alert.validate.label": "请输入库存提醒", "product.input.tax-type.validate.label": "请选择税种", "product.input.warehouse.validate.label": "请选择仓库", "product.success.create.message": "产品创建成功", "product.success.edit.message": "产品更新成功", "product.success.delete.message": "产品删除成功", "product.image.success.upload.message": "图片上传成功", "product.image.success.delete.message": "图片删除成功", "brands.title": "品牌", "brand.title": "品牌", "brand.create.title": "创建品牌", "brand.edit.title": "编辑品牌", "brand.input.code.label": "代码", "globally.input.change-logo.tooltip": "更改徽标", "globally.input.change-image.tooltip": "更改图片", "brand.table.brand-name.column.label": "品牌名称", "brand.table.product-count.column.label": "产品数量", "brand.input.name.valid.validate.label": "名称不得超过 50 个字符", "brand.success.create.message": "品牌创建成功", "brand.success.edit.message": "品牌更新成功", "brand.success.delete.message": "品牌删除成功", "product-categories.title": "产品类别", "product-category.title": "产品类别", "product-category.create.title": "创建产品类别", "product-category.edit.title": "编辑产品类别", "product-category.success.create.message": "产品类别创建成功", "product-category.success.edit.message": "产品类别更新成功", "product-category.success.delete.message": "产品类别删除成功", "expense-categories.title": "费用类别", "expense-category.title": "费用类别", "expense-category.create.title": "创建费用类别", "expense-category.edit.title": "编辑费用类别", "expense-category.success.create.message": "费用类别创建成功", "expense-category.success.edit.message": "费用类别更新成功", "expense-category.success.delete.message": "费用类别删除成功", "expenses.title": "费用", "expense.title": "费用", "expense.create.title": "创建费用", "expense.edit.title": "编辑费用", "expense.input.details.label": "详细信息", "expense.input.details.placeholder.label": "输入详细信息", "expense.input.amount.label": "金额", "expense.input.title.label": "费用标题", "expense.input.title.validate.label": "请输入费用标题", "expense.input.title.placeholder.label": "输入费用标题", "expense.input.amount.placeholder.label": "输入金额", "expense.input.warehouse.placeholder.label": "选择仓库", "expense.input.expense-category.placeholder.label": "选择费用类别", "expense.input.warehouse.validate.label": "请选择仓库", "expense.input.expense-category.validate.label": "请选择费用类别", "expense.input.amount.validate.label": "请输入金额", "expense.success.create.message": "费用创建成功", "expense.success.edit.message": "费用更新成功", "expense.success.delete.message": "费用删除成功", "roles.title": "角色", "roles.permissions.title": "角色/权限", "role.title": "角色", "role.create.title": "创建角色", "role.edit.title": "编辑角色", "role.select.all-permission.label": "所有权限", "role.input.permission.label": "权限", "role.input.name.validate.label": "请输入姓名", "role.input.name.valid.validate.label": "名称不得超过 50 个字符", "role.success.create.message": "角色创建成功", "role.success.edit.message": "角色更新成功", "role.success.delete.message": "角色删除成功", "units.title": "单位", "unit.title": "单位", "unit.create.title": "创建单位", "unit.edit.title": "编辑单元", "unit.modal.input.short-name.label": "简称", "unit.modal.input.short-name.placeholder.label": "输入短名称", "unit.modal.input.base-unit.label": "基本单位", "unit.modal.input.base-unit.placeholder.label": "选择基本单位", "unit.modal.input.short-name.validate.label": "请输入短名称", "unit.modal.input.short-name.valid.validate.label": "短名称不得超过 50 个字符", "unit.modal.input.base-unit.validate.label": "请选择基本单位", "unit.success.create.message": "单位创建成功", "unit.success.edit.message": "单元更新成功", "unit.success.delete.message": "单位删除成功", "currencies.title": "货币", "currency.title": "货币", "currency.create.title": "创建货币", "currency.edit.title": "编辑货币", "currency.modal.input.name.placeholder.label": "输入货币名称", "currency.modal.input.code.label": "代码", "currency.modal.input.code.placeholder.label": "输入货币代码", "currency.modal.input.symbol.label": "符号", "currency.modal.input.symbol.placeholder.label": "输入货币符号", "currency.modal.input.name.validate.label": "请输入货币名称", "currency.modal.input.code.validate.label": "请输入货币代码", "currency.modal.input.code.valid.validate.label": "代码不能超过 20 个字符", "currency.modal.input.symbol.validate.label": "请输入货币符号", "currency.success.create.message": "货币创建成功", "currency.success.edit.message": "货币更新成功", "currency.success.delete.message": "货币删除成功", "purchases.title": "采购", "purchases.purchases-details.title": "采购详情", "purchase.title": "购买", "purchase.create.title": "创建购买", "purchase.edit.title": "编辑购买", "purchase.select.warehouse.label": "仓库", "purchase.select.warehouse.placeholder.label": "选择仓库", "purchase.select.supplier.label": "供应商", "purchase.select.supplier.placeholder.label": "选择供应商", "purchase.select.warehouse.validate.label": "请选择仓库", "purchase.select.supplier.validate.label": "请选择供应商", "purchase.select.status.label": "状态", "purchase.select.status.placeholder.label": "选择状态", "purchase.order-item.table.label": "订购商品", "purchase.order-item.table.net-unit-cost.column.label": "净单位成本", "purchase.order-item.table.stock.column.label": "库存", "purchase.order-item.table.qty.column.label": "数量", "purchase.order-item.table.discount.column.label": "折扣", "purchase.order-item.table.tax.column.label": "税", "purchase.order-item.table.sub-total.column.label": "小计", "purchase.input.order-tax.label": "订单税", "purchase.input.shipping.label": "运费", "purchase.input.discount.validate.label": "请输入折扣", "purchase.input.order-tax.validate.label": "请输入订单税", "purchase.input.shipping.validate.label": "请输入运费", "purchase.product-modal.select.discount-type.label": "折扣类型", "discount-type.filter.percentage.label": "百分比", "discount-type.filter.fixed.label": "固定", "purchase.table.column.reference-code.label": "参考代码", "purchase.grant-total.label": "总计", "purchase.product-list.validate.message": "请将产品添加到列表中", "purchase.success.create.message": "购买创建成功", "purchase.success.edit.message": "购买更新成功", "purchase.success.delete.message": "购买删除成功", "purchase.placeholder.notes.input": "输入备注", "purchases.details.title": "购买详情", "purchase.detail.supplier.info": "供应商信息", "purchase.detail.purchase.info": "购买信息", "purchases.return.title": "购买退货", "purchase.return.create.title": "创建购买退货", "purchase.return.edit.title": "编辑购买退货", "purchase.return.success.create.message": "购买退货创建成功", "purchase.return.success.edit.message": "购买退货更新成功", "purchase.return.success.delete.message": "购买退货删除成功", "purchases.return.details.title": "购买退货详情", "settings.title": "设置", "settings.system-settings.title": "系统设置", "settings.system-settings.select.default-currency.label": "默认货币", "settings.system-settings.select.default-currency.placeholder.label": "选择默认货币", "settings.system-settings.input.default-email.label": "默认邮箱", "settings.system-settings.input.default-email.placeholder.label": "输入默认邮箱", "settings.system-settings.select.default-language.label": "默认语言", "settings.system-settings.select.default-language.placeholder.label": "选择默认语言", "settings.system-settings.select.default-customer.label": "默认客户", "settings.system-settings.select.default-customer.placeholder.label": "选择默认客户", "settings.system-settings.select.default-warehouse.label": "默认仓库", "settings.system-settings.select.default-warehouse.placeholder.label": "选择默认仓库", "settings.system-settings.input.change-logo.label": "更改徽标", "settings.system-settings.input.company-name.label": "公司名称", "settings.system-settings.input.company-name.placeholder.label": "输入公司名称", "settings.system-settings.input.company-phone.label": "公司电话", "settings.system-settings.input.company-phone.placeholder.label": "输入公司电话", "settings.system-settings.input. Developed-by.placeholder.label": "输入开发者", "settings.system-settings.input.footer.label": "页脚", "settings.system-settings.input.footer.placeholder.label": "输入页脚", "settings.payment-gateway.title": "支付网关", "settings.payment-gateway.input.stripe-key.label": "STRIPE_KEY", "settings.payment-gateway.input.stripe-key.placeholder.label": "如果您没有更改，请将此字段留空", "settings.payment-gateway.input.stripe-secret.label": "STRIPE_SECRET", "settings.payment-gateway.switch-btn.label": "删除 Stripe API 密钥", "settings.sms-configuration.title": "短信配置", "settings.sms-configuration.select.sms-gateway.label": "短信网关", "settings.sms-configuration.select.sms-gateway.placeholder.label": "选择短信网关", "settings.sms-configuration.input.twilio-sid.label": "TWILIO_SID", "settings.sms-configuration.input.twilio-token.label": "TWILIO_TOKEN", "settings.sms-configuration.select.twilio-from.label": "TWILIO_FROM", "settings.sms-configuration.select.twilio-from.placeholder.label": "输入 TWILIO FROM", "settings.sms-configuration.input.twilio-sid.placeholder.label": "输入 TWILIO SID", "settings.smtp-configuration.title": "SMTP 配置", "settings.smtp-configuration.input.host.label": "HOST", "settings.smtp-configuration.input.port.label": "端口", "settings.smtp-configuration.input.username.label": "用户名", "settings.smtp-configuration.input.password.label": "密码", "settings.smtp-configuration.input.encryption.label": "加密", "settings.clear-cache.title": "清除缓存", "settings.system-settings.select.default-currency.validate.label": "请选择货币", "settings.system-settings.input.company-name.validate.label": "请输入公司名称", "settings.system-settings.input.company-phone.validate.label": "请输入公司电话", "settings.system-settings.input.develop-by.validate.label": "请输入开发者", "settings.system-settings.input.footer.validate.label": "请输入城市", "settings.system-settings.select.default-language.validate.label": "请选择语言", "settings.system-settings.select.default-customer.validate.label": "请选择客户", "settings.system-settings.select.default-warehouse.validate.label": "请选择仓库", "settings.system-settings.select.address.validate.label": "请输入地址", "settings.system-settings.select.address.valid.validate.label": "地址不能超过 150 个字符", "settings.sms-configuration.select.sms-gateway.validate.label": "请选择短信网关", "settings.sms-configuration.input.twilio-sid.validate.label": "请输入 sid", "settings.sms-configuration.input.twilio-token.validate.label": "请输入令牌", "settings.sms-configuration.select.twilio-from.validate.label": "请输入 twillo from", "settings.smtp-configuration.input.host.validate.label": "请输入 smtp 主机", "settings.smtp-configuration.input.port.validate.label": "请输入 smtp 端口", "settings.smtp-configuration.input.username.validate.label": "请输入用户名", "settings.smtp-configuration.input.password.validate.label": "请输入密码", "settings.smtp-configuration.input.encryption.validate.label": "请输入加密", "settings.success.edit.message": "设置更新成功", "update-profile.input.full-name.label": "全名", "update-profile.title": "个人资料详情", "update-profile.tab.title": "更新配置文件", "update-profile.success.update.message": "配置文件更新成功", "change-password.input.current.label": "当前密码", "change-password.input.new.label": "新密码", "change-password.input.confirm.label": "确认密码", "change-password.input.current.placeholder.label": "输入当前密码", "change-password.input.new.placeholder.label": "输入新密码", "change-password.input.confirm.placeholder.label": "输入确认密码", "change-password.input.current.validate.label": "输入当前密码", "change-password.input.new.validate.label": "输入新密码", "change-password.input.confirm.validate.label": "输入确认密码", "change-password.input.confirm.valid.validate.label": "密码确认不匹配", "login-form.title": "登录", "login-form.login-btn.label": "登录", "login-form.forgot-password.label": "忘记密码？", "forgot-password-form.reset-link-btn.label": "发送密码重置链接", "forgot-password-form.success.reset-link.label": "我们已经通过电子邮件发送了您的密码重置链接！", "login.success.message": "登录成功。", "logout.success.message": "注销成功。", "change-language.update.success.message": "语言更新成功", "reset-password.title": "重置密码", "reset-password.password.validate.label": "确认密码和密码必须匹配", "reset-password.success.update.message": "您的密码已被重置！", "sales.title": "销售额", "sale.title": "销售", "sale.create.title": "创建销售", "sale.edit.title": "编辑销售", "sale.select.customer.label": "客户", "sale.select.customer.placeholder.label": "选择客户", "sale.select.customer.validate.label": "请选择客户", "sale.select.payment-status.placeholder": "选择付款状态", "sale.order-item.table.net-unit-price.column.label": "净单价", "sale.product.table.no-data.label": "没有可用数据", "sale.success.create.message": "销售创建成功", "sale.success.edit.message": "销售更新成功", "sale.success.delete.message": "销售删除成功", "sale.details.title": "销售详情", "sale.detail.customer.info": "客户信息", "sale.detail.invoice.info": "发票信息", "sales-return.title": "销售退货", "sale-return.title": "销售退货", "sale-return.create.title": "创建销售退货", "sale-return.edit.title": "编辑销售退货", "sale-return.success.create.message": "销售退货创建成功", "sale-return.success.edit.message": "销售退货更新成功", "sale-return.success.delete.message": "销售退货删除成功", "sale-return.details.title": "销售退货详情", "date-picker.filter.today.label": "今天", "date-picker.filter.this-week.label": "本周", "date-picker.filter.last-week.label": "上周", "date-picker.filter.this-month.label": "本月", "date-picker.filter.last-month.label": "上个月", "date-picker.filter.Custom-Range.label": "自定义范围", "date-picker.filter.reset.label": "重置", "date-picker.filter.apply.label": "应用", "date-picker.filter.placeholder.label": "选择日期", "bar.title": "酒吧", "line.title": "行", "filter.label": "过滤器", "reports.title": "报告", "warehouse.reports.title": "仓库报告", "sale.reports.title": "销售报告", "stock.reports.title": "股票报告", "purchase.reports.title": "购买报告", "top- sell-product.reports.title": "畅销产品报告", "globally.react-table.column.code.label": "代码", "print.barcode.title": "打印条码", "paper.size.title": "纸张尺寸", "paper.size.placeholder.label": "选择纸张尺寸", "globally.paper.size.validate.label": "请选择纸张尺寸", "print.validate.label": "请更新要打印的条形码", "current.stock.label": "当前库存", "stock.report.details.title": "库存报告详情", "print.title": "打印", "update.title": "更新", "preview.title": "预览", "toast.successful.title": "成功", "toast.error.title": "出了点问题！", "unit.filter.all.label": "全部", "unit.filter.piece.label": "一块", "unit.filter.meter.label": "米", "unit.filter.kilogram.label": "公斤", "status.filter.received.label": "已收到", "status.filter.pending.label": "待定", "status.filter.ordered.label": "有序", "payment-status.filter.paid.label": "付费", "payment-status.filter.unpaid.label": "未付", "excel.btn.label": "擅长", "pdf.btn.label": "PDF", "cash.label": "现金", "no-option.label": "没有选项", "select.payment-type.label": "支付类型", "payment-type.filter.cheque.label": "支票", "payment-type.filter.bank-transfer.label": "银行转账", "payment-type.filter.other.label": "其他", "paying-amount-title": "支付金额", "create-payment-title": "创建付款", "reference-placeholder-label": "输入参考", "input-Amount-to-pay-title": "要支付的金额", "paying-amount-validate-label": "支付金额应小于或等于要支付的金额", "edit-payment-title": "编辑付款", "no-product-found.label": "未找到产品", "sale.select.payment-type.placeholder": "选择支付类型", "globally.payment.type.validate.label": "请选择支付方式", "pos.payment.amount.exceeds.total.error": "付款金额不能超过总金额", "pos.payment.total.exceeds.grand.total.error": "付款总额超过总计", "globally.payment.details.validate.label": "请检查付款详细信息是否有错误", "product.quantity.alert.reports.title": "产品数量警报", "globally-saving-btn-label": "正在保存...", "payment-status.filter.partial.label": "部分", "dashboard.recentSales.total-product.label": "总产品", "adjustments.title": "调整", "adjustments.create.title": "创建调整", "adjustments.edit.title": "编辑调整", "adjustments.detail.title": "调整细节", "Adjustment.success.create.message": "调整创建成功", "Adjustment.success.edit.message": "调整更新成功", "Adjustment.success.delete.message": "调整删除成功", "login-form.go-to-sign-in.label": "返回登录", "pos-product.title": "产品", "pos-qty.title": "数量", "pos-price.title": "价格", "pos-sub-total.title": "小计", "pos-total-qty.title": "总数量", "pos-total.title": "总计", "pos-pay-now.btn": "立即付款", "pos-globally.search.field.label": "按代码名称扫描/搜索产品", "pos-make-Payment.title": "付款", "pos-received-amount.title": "收到金额", "pos-total-amount.title": "总金额", "pos-sale.detail.invoice.info": "发票", "pos-sale.detail.Phone.info": "电话", "pos-sale.detail.Paid-bt.title": "付款人", "pos-close-btn.title": "关闭", "pos-item.print.invoice.title": "项目", "pos-all.categories.label": "所有类别", "pos-all.brands.label": "所有品牌", "pos-no-product-available.label": "無可用產品", "pos-sale.select.discount-type.placeholder": "选择折扣类型", "pos-sale.select.sale-unit-type.placeholder": "选择销售单位类型", "pos-thank.you-slip.invoice": "感谢您与我们一起购物。请再次访问", "pos.payment.success.message": "支付成功", "transfers.title": "转会", "transfer.title": "转让", "transfer.create.title": "创建传输", "transfer.edit.title": "编辑传输", "transfer.from-warehouse.title": "从仓库", "transfer.to-warehouse.title": "到仓库", "pos.cash-payment.product-error.message": "请将产品添加到购物车", "pos.cash-payment.quantity-error.message": "请添加产品数量", "pos.cash-payment.tax-error.message": "请输入 0 到 100 之间的税值", "pos.cash-payment.total-amount-error.message": "折扣金额不应大于总金额", "pos.subtotal.small.title": "小计", "settings.system-settings.select.default-version-footer.placeholder.label": "在页脚显示版本号", "settings.system-settings.select.logo.placeholder.label": "在付款单中显示徽标", "settings.system-settings.select.appname-sidebar.placeholder.label": "在侧边栏中显示应用程序名称", "pos.cash-payment.sub-total-amount-error.message": "运费不得大于小计", "product.import.title": "导入产品", "globally.sample.download.label": "下载样本", "product-code.import.required-highlight.message": "代码必须不存在", "product-unit.import.required-highlight.message": "单位必须已经创建请使用单位全名", "globally.optional-input.validate.label": "可选字段", "reset.title": "重置", "reset.yes.title": "是的，重置", "reset.modal.msg": "你确定要重置吗", "dashboard.widget.today-total-purchases.label": "今天总购买量", "dashboard.widget.today-payment-received.label": "今天收到的总金额（销售额）", "dashboard.widget.today-total-sales.label": "今天总销售额", "dashboard.widget.today-total-expense.label": "今天总费用", "globally.file.validate.label": "请选择文件", "globally.csv-file.validate.label": "请选择 csv 文件", "file.success.upload.message": "文件上传成功", "transfer.success.create.message": "传输创建成功。", "transfer.success.edit.message": "传输更新成功。", "transfer.success.delete.message": "传输删除成功。", "transfer.select.warehouse.validate.message": "您不能在同一个仓库转移库存", "status.filter.complated.label": "已完成", "status.filter.sent.label": "已发送", "settings.prefixes-settings.input.purchases.placeholder.label": "PU", "settings.prefixes-settings.input.purchases-return.placeholder.label": "PR", "settings.prefixes-settings.input.sales.placeholder.label": "SL", "settings.prefixes-settings.input.salse-return.placeholder.label": "SR", "settings.prefixes-settings.input.expense.placeholder.label": "EX", "settings.prefixes-settings.input.purchases.validate.label": "请输入购买", "settings.prefixes-settings.input.purchases-return.validate.label": "请输入购买退货", "settings.prefixes-settings.input.sales.validate.label": "请输入销售额", "settings.prefixes-settings.input.salse-return.validate.label": "请输入销售退货", "settings.prefixes-settings.input.expense.validate.label": "请输入费用", "transfer.details.title": "转账详情", "setting.state.lable": "状态", "setting.postCode.lable": "邮政编码", "settings.system-settings.select.state.validate.label": "请选择状态", "settings.system-settings.select.country.validate.label": "请选择国家", "settings.system-settings.select.postcode.validate.label": "请输入邮政编码", "side-menu.empty.message": "没有找到匹配的记录", "product.export.title": "导出产品", "globally.input.content.label": "内容", "email-template.edit.title": "编辑电子邮件模板", "email-template.title": "电子邮件模板", "email-template.success.edit.message": "邮件模板更新成功。", "purchases.total.amount.title": "购买总金额", "purchases-return.total.amount.title": "购买退货总金额", "supplier.report.details.title": "供应商报告详情", "supplier.report.title": "供应商报告", "prefix.title": "前缀", "quotations.title": "报价", "create-quotation.title": "创建报价单", "edit-quotation.title": "编辑报价", "details-quotations.title": "报价详情", "quotation.success.create.message": "报价创建成功", "quotation.success.edit.message": "报价更新成功", "quotation.success.delete.message": "报价删除成功", "settings.system-settings.select.date-format.label": "日期格式", "quotation.title": "报价", "converted.status.label": "已转换", "mail-settings.sender-name.title": "发件人姓名", "mail-settings.title": "邮件设置", "mail-settings.success.edit.message": "邮件设置更新成功", "settings.system-settings.select.postcode.validate.length.label": "邮政编码长度不能超过 8", "best-customer.report.title": "最佳客户", "sms-template.edit.title": "编辑短信模板", "sms-template.title": "短信模板", "sms-template.success.edit.message": "短信模板更新成功。", "sms-content-variables.title": "短信内容变量", "email-content-variables.title": "电子邮件内容变量", "sms-content-text.error.message": "您已达到允许的最大字符数 300。", "sms-content.error.message": "内容必填", "profit-loss.reports.title": "盈亏", "global.revenue.title": "收入", "global.gross-profit.title": "毛利润", "global.payment-received.title": "收款", "global.payment-sent.title": "付款已发送", "global.net-payment.title": "支付网", "customer.report.details.title": "客户报告详情", "customer.report.title": "客户报告", "sale.payment.report.title": "销售付款", "sale.total.amount.title": "销售总金额", "sale-return.total.amount.title": "销售退货总金额", "sale-Due.total.amount.title": "到期总金额", "sale-paid.total.amount.title": "总支付金额", "sale-reference.title": "销售参考", "quotation.detail.invoice.info": "报价信息", "pepole.title": "人民", "more-report.option.title": "更多", "currency.icon.right.side.lable": "货币图标右侧", "pos.register-details.sell.title": "卖出", "total.sales.title": "总销售额", "email.status.edit.success.message": "邮件状态更新成功。", "sms.status.edit.success.message": "短信状态更新成功。", "sms-api.title": "短信 API", "key.lable": "密钥", "key.value.lable": "关键值", "url.lable": "网址", "mobile.key.lable": "手机钥匙", "message.key.lable": "消息密钥", "sms.status.lable": "短信状态", "active.status.lable": "活动", "in-active.status.lable": "不活跃", "sms.api.update.success.message": "短信接口更新成功。", "sale-return.product-qty.validate.message": "退货数量大于售出数量", "template.title": "模板", "pos.product-quantity-error.message": "没有更多可用数量。", "product-list.lable": "产品列表", "top-selling-product.reports.title": "最佳销售产品报告", "register.details.title": "注册详细信息", "register.total-sales.label": "总销售额", "register.total-refund.title": "总退款", "register.total-payment.title": "总付款", "register.product.sold.title": "售出产品详情", "register.product.sold.by.brand.title": "销售产品的详细信息（按品牌）", "print-barcode.show-company.label": "顯示商店名稱", "print-barcode.show-product-name.label": "显示产品名称", "print-barcode.show-price.label": "显示价格", "settings.system-settings.input.developed-by.label": "开发者", "product.input.quantity-limitation.label": "数量限制", "product.input.quantity-limitation.placeholder": "输入数量限制", "report-all.warehouse.label": "所有仓库", "setting.mail-mailer.lable": "邮递员", "setting.mail-host.lable": "邮件主机", "setting.mail-port.lable": "邮件端口", "setting.mail-user-name.lable": "邮件用户名", "setting.mail-password.lable": "邮件密码", "setting.mail-encryption.lable": "邮件加密", "pos.hold-list-btn.title": "抓住", "create-modal.yes.ok-btn": "好的", "hold-list.reference-number.placeholder": "请输入参考编号！", "create.hold-list.warning.message": "持有发票？相同的参考将替换旧列表（如果存在）！！", "hold-list.details.title": "保留列表", "hold-list-id.table.column.label": "ID", "hold-list-ref-id.table.column.label": "参考编号", "confirm-modal.msg": "你确定吗？", "hold-list.success.create.message": "保留列表创建成功", "hold-list.success.delete.message": "已成功删除保留列表", "sale.payment.create.success": "銷售付款創建成功", "sale.payment.edit.success": "銷售付款更新成功", "hold-list.reference-code.error": "参考代码字段是必需的。", "settings.clear-cache.success.message": "缓存清除成功", "product.product-in-stock.label": "有存货", "product-items.label": "项目", "Payload.key.lable": "有效载荷", "base-units.title": "基本单位", "base-unit.create.title": "创建基础单位", "base-unit.edit.title": "编辑基本单位", "base-unit.title": "基地单位", "base-unit.success.create.message": "基础单位创建成功", "base-unit.success.edit.message": "基地单位更新成功", "base-unit.success.delete.message": "基地单位删除成功", "DOB.input.label": "出生日期", "purchase.product.quantity.validate.label": "請輸入產品數量", "languages.title": "語言", "react-data-table.translation.column.label": "翻譯", "react-data-table.iso-date.column.label": "國際標準化組織代碼", "globally.input.iso-code.validate.label": "請輸入ISO代碼", "globally.input.iso-code.character.validate.label": "ISO 代碼長度應等於 2", "translation.manager.title": "翻譯經理", "language.updated.success.message": "語言更新成功", "language.deleted.success.message": "語言刪除成功", "language.edit.success.message": "語言修改成功", "language.save.success.message": "語言創建成功", "language.enabled.success.message": "語言啟用成功", "language.disabled.success.message": "語言停用成功", "language.current-language-disable.error.message": "您無法停用目前選擇的語言", "header.profile-menu.change-language.label": "改變語言", "language.title": "語言", "pos-paying-amount.title": "支付金額", "pos.change-return.label": "更改返回", "language.create.title": "創建語言", "purchase.less.recieving.ammout.error": "接收金額小於總計", "add-stock.title": "添加庫存", "product-quantity.add.title": "添加產品數量", "edit-translation.title": "編輯翻譯", "globally.input.cash-in-hand.label": "手頭現金", "globally.close-register.title": "關閉註冊", "register.closed.successfully.message": "註冊關閉成功。", "register.entry.added.successfully.message": "註冊條目添加成功。", "globally.total-cash.label": "總現金", "globally.input.note.label": "註釋", "globally.input.note.placeholder.label": "輸入筆記", "register.report.title": "註冊報告", "user-details.table.opened-on.row.label": "打開於", "user-details.table.closde-on.row.label": "關閉時間", "globally.input.cash-in-hand-while-fitting.label": "關閉時手頭現金", "pos.cclose-register.enter-total-cash.message": "請添加現金總額。", "register.is.still.open.message": "註冊仍然開放！！！", "Are.you.sure.you.want.to.go.to.dashboard.message": "您確定要轉到儀表板嗎？", "product.quantity.title": "产品数量", "pos.this.product.out.of.stock.message": "該產品缺貨", "pos.quantity.exceeds.quantity.available.in.stock.message": "數量超過庫存數量", "yes.modal.title": "是的", "no.modal.title": "不", "language.edit.title": "编辑语言", "globally.input.cash-in-hand-while-closing.label": "關閉時手頭現金", "select.user.label": "選擇用戶", "suppliers.import.title": "進口供應商", "customers.import.title": "進口客戶", "products.type.single-type.label": "單", "product.type.label": "產品類型", "product.type.placeholder.label": "選擇產品類型", "product.type.input.validation.error": "請選擇產品類型", "variation.name": "變化名稱", "variation.variation_types": "變化類型", "variations.title": "變化", "variation.title": "變化", "variation.create.title": "創建變化", "variation.edit.title": "編輯變化", "variation.input.name.label": "名稱", "variation.input.name.placeholder.label": "輸入名稱", "variation.input.name.validate.label": "請輸入名稱", "variation.success.create.message": "成功創建變化", "variation.success.edit.message": "成功編輯變化", "variation.success.delete.message": "成功刪除變化", "variation.types.title": "變化類型", "variation.type.title": "變化類型", "variation.type.input.name.placeholder.label": "請輸入變化類型", "variation.type.input.name.validate.label": "請輸入有效的變化類型", "variation.select.validation.error.message": "請選擇一種變化", "variation.type.select.validate.error.message": "請選擇變化類型", "products.warehouse.title": "產品倉庫", "barcode-symbol-uppercase-validation-message": "請僅輸入 Code 39 條碼的大寫字母和數字", "sale.product-qty.limit.validate.message": "您不能購買超過限制數量", "receipt-settings.title": "收據設定", "receipt-settings.show-warehouse.label": "顯示倉庫", "receipt-settings.show-email.label": "顯示電子郵件", "receipt-settings.show-address.label": "顯示位址", "receipt-settings.show-customer.label": "顯示客戶", "receipt-settings.show-phone.label": "顯示電話", "receipt-settings.show-discount-shipping.label": "顯示折扣和運費", "receipt-settings.success.edit.message": "收據設定更新成功", "receipt-settigns.input.note.validate.label": "請輸入備註", "receipt-settings.show-barcode.label": "在收據中顯示條碼", "receipt-settings.show-note.label": "顯示備註", "globally.submit-and-print-button": "提交並列印", "receipt-settings.show-product-code.label": "顯示產品代碼", "globally.footer.label": "版权所有", "logout.confirmation.label": "您確定要退出嗎？", "addition.title": "新增", "subtraction.title": "減法", "print-custom-barcode.title": "列印自訂條碼", "product.sku.label": "SKU/條碼", "add.stock.while.product.creation.title": "創建產品時新增庫存", "store.title": "商店", "create.store.title": "創建商店", "edit.store.title": "編輯商店", "store.name.title": "商店名稱", "store.name.placeholder.title": "輸入商店名稱", "store.name.validate.label": "請輸入商店名稱", "store.success.create.message": "商店創建成功", "store.success.edit.message": "商店更新成功", "store.success.delete.message": "商店刪除成功", "store.changed.message": "商店更改成功", "store.header.name.title": "商店", "select.all.store.title": "選擇所有商店", "store.field.must.required.validate": "商店欄位必須為必填項目", "store.assigned.title": "已指派商店", "no.store.title": "無商店資訊", "paid.amount.title": "已付金額", "taxes.title": "稅", "tax.title": "稅", "add.tax.title": "加稅金", "edit.tax.title": "編輯稅表", "tax.name.title": "稅務名稱", "tax.name.placeholder.title": "輸入稅務名稱", "tax.name.validate.title": "請輸入稅務名稱", "tax.value.title": "稅值", "tax.value.placeholder.title": "輸入稅值", "tax.value.validate.title": "請輸入稅額", "tax.deleted.success.message": "稅務刪除成功", "tax.edit.success.message": "稅費編輯成功", "tax.save.success.message": "稅務創建成功", "tax.name.unique.validate.title": "稅號已存在", "tax.value.unique.validate.title": "稅值已存在", "tax.show.on.receipt.pdf.title": "在收據/PDF 上顯示", "pos.settings.title": "POS 設定", "enable.pos.sound.title": "啟用POS點擊聲音", "show.out.of.stock.product.in.pos": "在 POS 中顯示缺貨產品", "pos.sound.title": "POS 聲音", "upload.audio.title": "上傳音訊", "pos.audio.required": "請上傳音訊", "date.of.birth.title": "出生日期", "customer.details.title": "顧客詳情", "pos.audio.length.tooltip.title": "音訊長度應小於 3 秒。", "select.date.of.birth": "選擇您的出生日期", "item.deleted.success.message": "專案已成功刪除", "payment.method.save.success.message": "付款方式建立成功", "payment.method.edit.success.message": "付款方式編輯成功", "payment.method.deleted.success.message": "付款方式刪除成功", "payment.method.name.unique.validate.title": "付款方式名稱已存在", "show.tax.title": "顯示稅費", "expiry.date.title": "到期日", "expiry.date.placeholder.title": "輸入到期日", "payment.method.title": "付款方式", "dual.screen.settings.title": "雙螢幕設定", "dual.screen.display.header.title": "顯示標題", "dual.screen.display.header.placeholder.title": "輸入顯示標題", "please.enter.display.header.title": "請輸入顯示標題", "carousel.image.title": "輪播圖片", "validation.you.can.upload.maximum.images": "您最多可以上傳 5 張圖片", "upload.maximum.images": "最多上傳五張圖片。", "no.customer.selected.title": "未選擇客戶", "send.test.email.title": "發送測試郵件", "send.test.email.success.message": "測試郵件發送成功", "globally.receipt.download.label": "下載收據", "load.more.title": "載入更多", "pos.edit.sale.title": "您確定要編輯此銷售嗎？", "sale.payment.total-exceed.validate.message": "總付款金額不得超過總計。", "globally.input.width.label": "寬度", "globally.input.height.label": "高度", "globally.input.width.validate.label": "請輸入寬度", "globally.input.height.validate.label": "請輸入高度"}