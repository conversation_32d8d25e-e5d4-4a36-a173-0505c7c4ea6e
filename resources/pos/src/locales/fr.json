{"dashboard.title": "Tableau de bord", "header.pos.title": "PDV", "header.profile-menu.profile.label": "Profil", "header.profile-menu.change-password.label": "Changer le mot de passe", "header.profile-menu.logout.label": "Se déconnecter", "product.categories.title": "catégories de produits", "expense.categories.title": "Catégories de dépenses", "dashboard.salesReturn.title": "<PERSON><PERSON> de vente", "dashboard.top-customers.title": "Les 5 meilleurs clients", "dashboard.purchaseReturn.title": "Retour des achats", "dashboard.ThisWeekSales&Purchases.title": "Ventes et achats de cette semaine", "dashboard.TopSellingProducts.title": "Produits les plus vendus", "dashboard.stockAlert.title": "Alerte Stock", "dashboard.stockAlert.code.label": "Code", "dashboard.stockAlert.product.label": "Produit", "dashboard.stockAlert.warehouse.label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dashboard.stockAlert.quantity.label": "Quantité", "dashboard.stockAlert.alertQuantity.label": "Quantité d'alerte", "dashboard.grantTotal.label": "Total", "dashboard.recentSales.reference.label": "Référence", "dashboard.recentSales.customer.label": "Client", "dashboard.recentSales.status.label": "Statut", "dashboard.recentSales.paid.label": "<PERSON><PERSON>", "dashboard.recentSales.due.label": "Exigible", "dashboard.recentSales.paymentStatus.label": "Statut de paiement", "dashboard.recentSales.title": "<PERSON><PERSON>s r<PERSON>es", "dashboard.PaymentSentReceived.title": "Paiement envoyé et reçu", "dataTable.searchBar.placeholder.label": "Recherche", "settings.select.language.label": "<PERSON><PERSON>", "settings.select.language.placeholder": "<PERSON><PERSON>", "users.title": "Utilisateurs", "user.create.title": "C<PERSON>er un utilisateur", "user.edit.title": "Modifier l'utilisateur", "user.input.first-name.label": "Prénom", "user.input.last-name.label": "Nom de famille", "user.input.email.label": "E-mail", "user.input.phone-number.label": "Numéro de téléphone", "user.input.password.label": "Mot de passe", "user.input.confirm-password.label": "Confirmez le mot de passe", "user.input.role.label": "R<PERSON><PERSON>", "user.input.first-name.placeholder.label": "Entrez votre prénom", "user.input.last-name.placeholder.label": "Entrer le nom de famille", "user.input.email.placeholder.label": "Entrez l'e-mail", "user.input.phone-number.placeholder.label": "Entrez le numéro de téléphone", "user.input.password.placeholder.label": "Entrer le mot de passe", "user.input.confirm-password.placeholder.label": "<PERSON><PERSON><PERSON> Confirmer le mot de passe", "user.input.role.placeholder.label": "Choisissez le rôle", "users.table.user.column.title": "Utilisa<PERSON>ur", "users.table.phone-number.column.title": "Numéro de téléphone", "users.table.role.column.title": "R<PERSON><PERSON>", "users.table.date.column.title": "Date de création", "user-details.title": "Détails de l'utilisateur", "user-details.table.title": "<PERSON><PERSON><PERSON><PERSON>", "user-details.table.created-on.row.label": "<PERSON><PERSON><PERSON>", "user.input.first-name.validate.label": "Veuillez entrer le prénom", "user.input.last-name.validate.label": "Veuillez entrer le nom de famille", "user.input.email.validate.label": "Veuillez saisir l'adresse e-mail", "user.input.email.valid.validate.label": "Veuillez saisir une adresse e-mail valide", "user.input.phone-number.validate.label": "Veuillez entrer le numéro de téléphone", "user.input.password.validate.label": "Veuillez entrer le mot de passe", "user.input.confirm-password.validate.label": "Veuillez saisir le mot de passe de confirmation", "user.input.role.validate.label": "Veuillez sélectionner un rôle", "user.success.create.message": "Utilisateur c<PERSON>é avec succès", "user.success.edit.message": "Utilisateur mis à jour avec succès", "user.success.delete.message": "Utilisateur supprimé avec succès", "react-data-table.searchbar.placeholder": "Recherche", "react-data-table.action.column.label": "Action", "react-data-table.date.column.label": "Date", "react-data-table.no-record-found.label": "Il n'y a aucun enregistrement à afficher", "react-data-table.records-per-page.label": "Enregistrements par page", "delete-modal.title": "Supprimer!", "delete-modal.msg": "Voulez-vous vraiment supprimer ce", "delete-modal.yes-btn": "<PERSON>ui, Su<PERSON><PERSON><PERSON>!", "delete-modal.no-btn": "Non, Annuler", "suppliers.title": "Fournisseurs", "supplier.title": "Fournisseur", "supplier.create.title": "<PERSON><PERSON><PERSON> un fournis<PERSON>ur", "supplier.edit.title": "Modifier le fournisseur", "supplier.table.name.column.title": "Nom", "supplier.table.phone-number.column.title": "Numéro de téléphone", "supplier.table.email.column.title": "E-mail", "supplier.table.address.column.title": "<PERSON><PERSON><PERSON>", "supplier.success.create.message": "Fournisseur créé avec succès", "supplier.success.edit.message": "Fournisseur mis à jour avec succès", "supplier.success.delete.message": "Fournisseur supprimé avec succès", "globally.input.name.label": "Nom", "globally.input.name.placeholder.label": "Entrez le nom", "globally.react-table.column.created-date.label": "<PERSON><PERSON><PERSON>", "globally.react-table.column.payment-type.label": "Type de paiement", "globally.input.email.label": "E-mail", "globally.input.email.placeholder.label": "Entrez l'e-mail", "globally.input.phone-number.label": "Numéro de téléphone", "globally.input.phone-number.placeholder.label": "Entrez le numéro de téléphone", "globally.input.country.label": "Pays", "globally.input.country.placeholder.label": "Entrez le pays", "globally.input.city.label": "Ville", "globally.input.city.placeholder.label": "Entrez la ville", "globally.input.address.label": "Address", "globally.input.address.placeholder.label": "En<PERSON>r l'adresse", "globally.input.change-logo.tooltip": "Changer de logo", "globally.input.change-image.tooltip": "Changer l'image", "globally.type.label": "Taper", "globally.back-btn": "<PERSON><PERSON><PERSON>", "globally.save-btn": "Sauver", "globally.cancel-btn": "Annuler", "globally.edit-btn": "Modifier", "globally.submit-btn": "So<PERSON><PERSON><PERSON>", "globally.input.notes.label": "<PERSON><PERSON><PERSON>", "globally.input.notes.placeholder.label": "Saisir des remarques", "globally.loading.label": "<PERSON>'il vous plaît, attendez...", "globally.input.name.validate.label": "Veuillez entrer le nom", "globally.input.email.validate.label": "Veuillez saisir l'adresse e-mail", "globally.input.email.valid.validate.label": "Veuillez saisir une adresse e-mail valide", "globally.input.country.validate.label": "Veuillez entrer le pays", "globally.input.city.validate.label": "Veuillez entrer la ville", "globally.input.phone-number.validate.label": "Veuillez entrer le numéro de téléphone", "globally.input.address.validate.label": "Veuillez entrer l'adresse", "globally.input.notes.validate.label": "Les notes ne doivent pas dépasser 100 caractères", "globally.require-input.validate.label": "Ce champ est obligatoire", "globally.date.validate.label": "Veuillez entrer la date", "globally.tax-length.validate.label": "La taxe ne doit pas être supérieure à 100", "globally.discount-length.validate.label": "La remise ne doit pas être supérieure à 100", "globally.discount-cost-length.validate.label": "La remise ne doit pas être supérieure au coût du produit", "globally.discount-price-length.validate.label": "La remise ne doit pas être supérieure au prix du produit", "globally.edit.tooltip.label": "Modifier", "globally.delete.tooltip.label": "<PERSON><PERSON><PERSON><PERSON>", "globally.view.tooltip.label": "Voir", "globally.product-quantity.validate.message": "Veuillez ajouter la quantité de produit", "globally.product-already-added.validate.message": "Ce produit a déjà été ajouté", "globally.status.validate.label": "Veuillez sélectionner le statut", "globally.payment.status.validate.label": "Veuillez sélectionner le statut du paiement", "globally.detail.reference": "Référence", "globally.detail.status": "Statut", "globally.detail.warehouse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "globally.detail.payment.status": "Statut de paiement", "globally.detail.company.info": "Information d'entreprise", "globally.detail.order.summary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> commande", "globally.detail.product": "Produit", "globally.detail.net-unit-cost": "Coût unitaire net", "globally.detail.net-unit-price": "Prix unitaire net", "globally.detail.quantity": "Quantité", "globally.detail.unit-cost": "Coût unitaire", "globally.detail.unit-price": "Prix unitaire", "globally.detail.discount": "Remise", "globally.detail.tax": "<PERSON><PERSON><PERSON><PERSON>", "globally.detail.subtotal": "Total", "globally.detail.order.tax": "Taxe de commande", "globally.detail.shipping": "Expédition", "globally.detail.grand.total": "Total", "globally.detail.paid": "<PERSON><PERSON>", "globally.detail.due": "Exigible", "globally.show.payment.label": "Afficher les paiements", "globally.pdf.download.label": "Télécharger le PDF", "globally.search.field.label": "Rechercher un produit par nom de code", "customers.title": "Les clients", "customer.title": "Client", "customer.create.title": "<PERSON><PERSON><PERSON> un client", "customer.edit.title": "Modifier le client", "customer.success.create.message": "Client créé avec succès", "customer.success.edit.message": "Client mis à jour avec succès", "customer.success.delete.message": "Client supprimé avec succès", "warehouse.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warehouse.create.title": "<PERSON><PERSON><PERSON> un entrepôt", "warehouse.edit.title": "Modifier l'entrepôt", "warehouse.input.zip-code.label": "Code postal", "warehouse.input.zip-code.placeholder.label": "Entrer le code postal", "warehouse.success.create.message": "Entrepôt créé avec succès", "warehouse.success.edit.message": "Entrepôt mis à jour avec succès", "warehouse.success.delete.message": "Entrepôt supprimé avec succès", "products.title": "Des produits", "product.title": "Produit", "product.create.title": "Créer un produit", "product.edit.title": "Modifier le produit", "product.input.code.label": "Code", "product.input.code.placeholder.label": "Entrez le code", "product.input.product-category.label": "catégorie de produit", "product.input.product-category.placeholder.label": "Choisissez la catégorie de produit", "product.input.brand.label": "Marque", "product.input.brand.placeholder.label": "Choisissez la marque", "product.input.barcode-symbology.label": "Symbologie des codes-barres", "product.input.barcode-symbology.placeholder.label": "Choisissez la symbologie du code-barres", "product.input.product-cost.label": "Coût du produit", "product.input.product-cost.placeholder.label": "Entrez le coût du produit", "product.input.product-price.label": "Prix du produit", "product.input.product-price.placeholder.label": "Entrez le prix du produit", "product.input.product-unit.label": "Produit Unité", "product.input.product-unit.placeholder.label": "Choisissez l'unité de produit", "product.input.sale-unit.label": "<PERSON>é de vente", "product.input.sale-unit.placeholder.label": "Choisissez l'unité de vente", "product.input.purchase-unit.label": "<PERSON><PERSON>", "product.input.purchase-unit.placeholder.label": "Choisissez l'unité d'achat", "product.input.stock-alert.label": "Alerte Stock", "product.input.stock-alert.placeholder.label": "Entrez l'alerte de stock", "product.input.order-tax.label": "Taxe de commande", "product.input.order-tax.placeholder.label": "Entrer la taxe sur la commande", "product.input.order-tax.validate.label": "Veuillez entrer la taxe de commande", "product.input.order-tax.valid.validate.label": "La taxe ne doit pas être supérieure à 100", "product.input.tax-type.label": "Type de taxe", "tax-type.filter.exclusive.label": "Exclusif", "tax-type.filter.inclusive.label": "<PERSON><PERSON><PERSON>", "product.input.tax-type.placeholder.label": "Choisissez le type de taxe", "product.input.warehouse.placeholder.label": "Choisissez <PERSON>", "product.input.multiple-image.label": "Images multiples", "product.table.image.column.label": "Image", "product.table.price.column.label": "Prix", "product.product-details.title": "détails du produit", "product.product-details.code-product.label": "Code Produit", "product.product-details.category.label": "<PERSON><PERSON><PERSON><PERSON>", "product.product-details.cost.label": "Coût", "product.product-details.unit.label": "Unité", "product.product-details.tax.label": "<PERSON><PERSON><PERSON><PERSON>", "product.input.code.validate.label": "<PERSON><PERSON><PERSON><PERSON> entrer le code", "product.input.product-category.validate.label": "Veuillez sélectionner la catégorie de produit", "product.input.brand.validate.label": "Veuillez sélectionner la marque", "product.input.barcode-symbology.validate.label": "Veuillez sélectionner la symbologie du code-barres", "product.input.product-cost.validate.label": "Veuillez entrer le coût du produit", "product.input.product-price.validate.label": "Veuillez entrer le prix du produit", "product.input.product-unit.validate.label": "<PERSON><PERSON><PERSON>z sélectionner l'unité de produit", "product.input.sale-unit.validate.label": "Veuillez sélectionner l'unité de vente", "product.input.purchase-unit.validate.label": "Veuillez sélectionner l'unité d'achat", "product.input.stock-alert.validate.label": "Veuillez saisir l'alerte de stock", "product.input.tax-type.validate.label": "Veuillez sélectionner le type de taxe", "product.input.warehouse.validate.label": "Veuillez sélectionner l'entrepôt", "product.success.create.message": "Produit créé avec succès", "product.success.edit.message": "Produit mis à jour avec succès", "product.success.delete.message": "Produit supprimé avec succès", "product.image.success.upload.message": "Image téléchargée avec succès", "product.image.success.delete.message": "Image supprimée avec succès", "brands.title": "marques", "brand.title": "Marque", "brand.create.title": "<PERSON><PERSON><PERSON> une marque", "brand.edit.title": "Modifier la marque", "brand.table.brand-name.column.label": "Marque", "brand.table.product-count.column.label": "Nombre de produits", "brand.input.name.valid.validate.label": "Le nom ne doit pas dépasser 50 caractères", "brand.success.create.message": "<PERSON><PERSON> c<PERSON> avec succès", "brand.success.edit.message": "<PERSON><PERSON> mise à jour avec succès", "brand.success.delete.message": "Marque supprimée avec succès", "product-categories.title": "catégories de produits", "product-category.title": "catégorie de produit", "product-category.create.title": "<PERSON><PERSON>er une catégorie de produit", "product-category.edit.title": "Modifier la catégorie de produit", "product-category.success.create.message": "Catégorie de produit créée avec succès", "product-category.success.edit.message": "Catégorie de produit mise à jour avec succès", "product-category.success.delete.message": "Catégorie de produit supprimée avec succès", "expense-categories.title": "Catégories de dépenses", "expense-category.title": "<PERSON><PERSON><PERSON><PERSON>", "expense-category.create.title": "<PERSON><PERSON><PERSON> une catégorie de d<PERSON>", "expense-category.edit.title": "Modifier la catégorie de d<PERSON>penses", "expense-category.success.create.message": "Catégorie de dépenses créée avec succès", "expense-category.success.edit.message": "Catégorie de dépenses mise à jour avec succès", "expense-category.success.delete.message": "Catégorie de dépenses supprimée avec succès", "expenses.title": "<PERSON>é<PERSON>ses", "expense.title": "<PERSON><PERSON>", "expense.create.title": "<PERSON><PERSON><PERSON> une dépense", "expense.edit.title": "Modifier la dépense", "expense.input.details.label": "<PERSON>", "expense.input.details.placeholder.label": "Entrez les détails", "expense.input.amount.label": "<PERSON><PERSON>", "expense.input.amount.placeholder.label": "<PERSON><PERSON><PERSON> le montant", "expense.input.warehouse.placeholder.label": "Choisissez <PERSON>", "expense.input.expense-category.placeholder.label": "Choisissez la catégorie de dépenses", "expense.input.table.reference.column.label": "Référence", "expense.input.title.label": "<PERSON><PERSON><PERSON> <PERSON> d<PERSON>", "expense.input.title.validate.label": "Veuillez entrer le titre de la dépense", "expense.input.title.placeholder.label": "Entrez le titre de la dépense", "expense.input.warehouse.validate.label": "Veuillez sélectionner l'entrepôt", "expense.input.expense-category.validate.label": "Veuillez sélectionner la catégorie de dépenses", "expense.input.amount.validate.label": "Veuillez entrer le montant", "expense.success.create.message": "<PERSON>épense créée avec succès", "expense.success.edit.message": "<PERSON><PERSON><PERSON><PERSON> mise à jour avec succès", "expense.success.delete.message": "Dépense supprimée avec succès", "roles.title": "<PERSON><PERSON><PERSON>", "roles.permissions.title": "Rôles/Autorisations", "role.title": "R<PERSON><PERSON>", "role.create.title": "<PERSON><PERSON>er un rôle", "role.edit.title": "Modifier le rôle", "role.select.all-permission.label": "Toutes les autorisations", "role.input.permission.label": "Autorisations", "role.input.name.validate.label": "Veuillez entrer le nom", "role.input.name.valid.validate.label": "Le nom ne doit pas dépasser 50 caractères", "role.success.create.message": "Rôle c<PERSON>é avec succès", "role.success.edit.message": "Rôle mis à jour avec succès", "role.success.delete.message": "Rôle supprimé avec succès", "units.title": "Unités", "unit.title": "Unité", "unit.create.title": "<PERSON><PERSON>er une unité", "unit.edit.title": "Modifier l'unité", "unit.modal.input.short-name.label": "Nom court", "unit.modal.input.short-name.placeholder.label": "Entrez le nom court", "unit.modal.input.base-unit.label": "Unité de base", "unit.modal.input.base-unit.placeholder.label": "Choisissez l'unité de base", "unit.modal.input.short-name.validate.label": "Veuillez entrer un nom court", "unit.modal.input.short-name.valid.validate.label": "Le nom court ne doit pas dépasser 50 caractères", "unit.modal.input.base-unit.validate.label": "Veuillez choisir l'unité de base", "unit.success.create.message": "Unité créée avec succès", "unit.success.edit.message": "Unité mise à jour avec succès", "unit.success.delete.message": "Unité supprimée avec succès", "currencies.title": "Devi<PERSON>", "currency.title": "Monnaie", "currency.create.title": "<PERSON><PERSON><PERSON> une devise", "currency.edit.title": "Modifier la devise", "currency.modal.input.name.placeholder.label": "Entrez le nom de la devise", "currency.modal.input.code.label": "Code", "currency.modal.input.code.placeholder.label": "Entrez le code de devise", "currency.modal.input.symbol.label": "Symbole", "currency.modal.input.symbol.placeholder.label": "Entrer le symbole monétaire", "currency.modal.input.name.validate.label": "<PERSON><PERSON><PERSON><PERSON> entrer le nom de la devise", "currency.modal.input.code.validate.label": "<PERSON><PERSON><PERSON><PERSON> entrer le code de devise", "currency.modal.input.code.valid.validate.label": "Le code ne doit pas dépasser 20 caractères", "currency.modal.input.symbol.validate.label": "<PERSON><PERSON><PERSON><PERSON> entrer le symbole monétaire", "currency.success.create.message": "<PERSON><PERSON> c<PERSON> avec succès", "currency.success.edit.message": "<PERSON><PERSON> mise à jour avec succès", "currency.success.delete.message": "Devise supprimée avec succès", "purchases.title": "Achats", "purchase.title": "<PERSON><PERSON><PERSON>", "purchase.create.title": "<PERSON><PERSON><PERSON> un achat", "purchase.edit.title": "Modifier l'achat", "purchase.select.warehouse.label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "purchase.select.warehouse.placeholder.label": "Choisissez <PERSON>", "purchase.select.supplier.label": "Fournisseur", "purchase.select.supplier.placeholder.label": "Choisissez le fournisseur", "purchase.select.status.label": "Statut", "purchase.select.status.placeholder.label": "Choisissez le statut", "purchase.order-item.table.label": "Items commandés", "purchase.order-item.table.net-unit-cost.column.label": "Coût unitaire net", "purchase.order-item.table.stock.column.label": "Stocker", "purchase.order-item.table.qty.column.label": "Qté", "purchase.order-item.table.discount.column.label": "Remise", "purchase.order-item.table.tax.column.label": "<PERSON><PERSON><PERSON><PERSON>", "purchase.order-item.table.sub-total.column.label": "Total", "purchase.input.order-tax.label": "Taxe de commande", "purchase.input.shipping.label": "Expédition", "purchase.placeholder.notes.input": "Saisir des remarques", "purchase.product-list.validate.message": "Veuillez ajouter le produit à la liste", "purchase.select.warehouse.validate.label": "Veuillez sélectionner l'entrepôt", "purchase.select.supplier.validate.label": "Veuillez sélectionner le fournisseur", "purchase.input.discount.validate.label": "Veuillez entrer la remise", "purchase.input.order-tax.validate.label": "Veuillez entrer la taxe de commande", "purchase.input.shipping.validate.label": "Veuillez entrer l'expédition", "purchase.product-modal.select.discount-type.label": "Type de remise", "discount-type.filter.percentage.label": "Pourcentage", "discount-type.filter.fixed.label": "Fixé", "purchase.table.column.reference-code.label": "Code de référence", "purchase.grant-total.label": "Total", "purchase.success.create.message": "<PERSON><PERSON><PERSON> c<PERSON> avec succès", "purchase.success.edit.message": "<PERSON><PERSON><PERSON> mis à jour avec succès", "purchase.success.delete.message": "Achat supprimé avec succès", "purchases.details.title": "<PERSON><PERSON><PERSON> de l'achat", "purchase.detail.supplier.info": "Informations sur le fournisseur", "purchase.detail.purchase.info": "Informations d'achat", "purchases.return.title": "Achats Retours", "purchase.return.create.title": "<PERSON><PERSON>er un retour d'achat", "purchase.return.edit.title": "Modifier le retour d'achat", "purchase.return.success.create.message": "Retour d'achat créé avec succès", "purchase.return.success.edit.message": "Retour d'achat mis à jour avec succès", "purchase.return.success.delete.message": "Retour d'achat supprimé avec succès", "purchases.return.details.title": "<PERSON><PERSON><PERSON> du retour d'achat", "settings.title": "Réglages", "settings.system-settings.title": "Les paramètres du système", "settings.system-settings.select.default-currency.label": "devise par défaut", "settings.system-settings.select.default-currency.placeholder.label": "Choisissez la devise par défaut", "settings.system-settings.input.default-email.label": "E-mail par défaut", "settings.system-settings.input.default-email.placeholder.label": "Entrez l'e-mail par défaut", "settings.system-settings.select.default-language.label": "Langage par défaut", "settings.system-settings.select.default-language.placeholder.label": "Choisissez la langue par défaut", "settings.system-settings.select.default-customer.label": "Client par défaut", "settings.system-settings.select.default-customer.placeholder.label": "Choisissez le client par défaut", "settings.system-settings.select.default-warehouse.label": "Entrepôt par défaut", "settings.system-settings.select.default-warehouse.placeholder.label": "Choisissez l'entrepôt par défaut", "settings.system-settings.input.change-logo.label": "Changer de logo", "settings.system-settings.input.company-name.label": "Nom de l'entreprise", "settings.system-settings.input.company-name.placeholder.label": "Entrez le nom de l'entreprise", "settings.system-settings.input.company-phone.label": "Téléphone de l'entreprise", "settings.system-settings.input.company-phone.placeholder.label": "Entrer le téléphone de l'entreprise", "settings.system-settings.input.developed-by.label": "Développé par", "settings.system-settings.input.developed-by.placeholder.label": "<PERSON><PERSON>z Développé par", "settings.system-settings.input.footer.label": "Bas de page", "settings.system-settings.input.footer.placeholder.label": "Entrez le pied de page", "settings.payment-gateway.title": "Passerelle de paiement", "settings.payment-gateway.input.stripe-key.label": "CLÉ À RAYURES", "settings.payment-gateway.input.stripe-key.placeholder.label": "Veuillez laisser ce champ vide si vous ne l'avez pas modifié", "settings.payment-gateway.input.stripe-secret.label": "RAYURE SECRET", "settings.sms-configuration.select.sms-gateway.placeholder.label": "SMS Ağ Geçidini Seçin", "settings.payment-gateway.switch-btn.label": "Supprimer les clés API Stripe", "settings.sms-configuration.title": "Configuration SMS", "settings.sms-configuration.select.sms-gateway.label": "Passerelle SMS", "settings.sms-configuration.input.twilio-sid.label": "TWILIO SID", "settings.sms-configuration.input.twilio-token.label": "JETON TWILIO", "settings.sms-configuration.select.twilio-from.label": "DEPUIS", "settings.sms-configuration.select.twilio-from.placeholder.label": "Entrez TWILIO DE", "settings.sms-configuration.input.twilio-sid.placeholder.label": "Entrez TWILIO SID", "settings.smtp-configuration.title": "Paramétrage SMTP", "settings.smtp-configuration.input.host.label": "HÔTE", "settings.smtp-configuration.input.port.label": "PORT", "settings.smtp-configuration.input.username.label": "Nom d'utilisateur", "settings.smtp-configuration.input.password.label": "Mot de passe", "settings.smtp-configuration.input.encryption.label": "Chiffrement", "settings.clear-cache.title": "Vider le cache", "settings.success.edit.message": "Paramètre mis à jour avec succès", "settings.system-settings.select.default-currency.validate.label": "Veuillez sélectionner la devise", "settings.system-settings.input.company-name.validate.label": "Veuillez saisir le nom de l'entreprise", "settings.system-settings.input.company-phone.validate.label": "Veuillez saisir le numéro de téléphone de l'entreprise", "settings.system-settings.input.developed-by.validate.label": "Veuillez entrer développé par", "settings.system-settings.input.footer.validate.label": "Veuillez entrer la ville", "settings.system-settings.select.default-language.validate.label": "Veuillez entrer la langue par défaut", "settings.system-settings.select.default-customer.validate.label": "Veuillez sélectionner le client par défaut", "settings.system-settings.select.default-warehouse.validate.label": "Veuillez sélectionner l'entrepôt par défaut", "settings.system-settings.input.address.validate.label": "Veuillez entrer l'adresse", "settings.system-settings.select.address.valid.validate.label": "L'adresse ne doit pas dépasser 150 caractères", "settings.sms-configuration.select.sms-gateway.validate.label": "Veuillez sélectionner la passerelle SMS", "settings.sms-configuration.input.twilio-sid.validate.label": "Veuillez saisir sid", "settings.sms-configuration.input.twilio-token.validate.label": "Veuillez entrer le jeton", "settings.sms-configuration.select.twilio-from.validate.label": "Veuillez entrer sergé de", "settings.smtp-configuration.input.host.validate.label": "Veuillez entrer l'hôte smtp", "settings.smtp-configuration.input.port.validate.label": "Veuillez entrer le port smtp", "settings.smtp-configuration.input.username.validate.label": "Veuillez entrer le nom d'utilisateur", "settings.smtp-configuration.input.password.validate.label": "Veuillez entrer le mot de passe", "settings.smtp-configuration.input.encryption.validate.label": "Veu<PERSON>z entrer le cryptage", "update-profile.input.full-name.label": "Nom complet", "update-profile.title": "<PERSON><PERSON><PERSON> du profil", "update-profile.tab.title": "Mettre à jour le profil", "update-profile.success.update.message": "Mise à jour du profil réussie", "change-password.input.current.label": "Mot de passe actuel", "change-password.input.new.label": "nouveau mot de passe", "change-password.input.confirm.label": "Confirmez le mot de passe", "change-password.input.current.placeholder.label": "Saisissez le mot de passe actuel", "change-password.input.new.placeholder.label": "Entrez un nouveau mot de passe", "change-password.input.confirm.placeholder.label": "<PERSON><PERSON><PERSON> Confirmer le mot de passe", "change-password.input.current.validate.label": "Saisissez le mot de passe actuel", "change-password.input.new.validate.label": "Entrez un nouveau mot de passe", "change-password.input.confirm.validate.label": "Entrez le mot de passe de confirmation", "change-password.input.confirm.valid.validate.label": "La confirmation du mot de passe ne correspond pas", "login-form.title": "S'identifier", "login-form.login-btn.label": "Connexion", "login-form.forgot-password.label": "Mot de passe oublié ?", "forgot-password-form.reset-link-btn.label": "Envoyer le lien de réinitialisation du mot de passe", "forgot-password-form.success.reset-link.label": "Nous avons envoyé votre lien de réinitialisation de mot de passe par e-mail!", "login.success.message": "Déconnectez-vous avec succès.", "logout.success.message": "Déconnectez-vous avec succès.", "change-language.update.success.message": "<PERSON>ue mise à jour avec succès", "reset-password.title": "réinitialiser le mot de passe", "reset-password.password.validate.label": "Le mot de passe de confirmation et le mot de passe doivent correspondre", "reset-password.success.update.message": "votre mot de passe a été réinitialisé!", "sales.title": "<PERSON><PERSON><PERSON>", "sale.title": "Vente", "sale.create.title": "<PERSON><PERSON><PERSON> une vente", "sale.edit.title": "Modifier la vente", "sale.select.customer.label": "Client", "sale.select.customer.placeholder.label": "Choisissez le client", "sale.select.customer.validate.label": "Veuillez sélectionner le client", "sale.select.payment-status.placeholder": "Choisissez le statut du paiement", "sale.order-item.table.net-unit-price.column.label": "Prix unitaire net", "sale.product.table.no-data.label": "Pas de données disponibles", "sale.success.create.message": "Vente créée avec succès", "sale.success.edit.message": "Vente mise à jour avec succès", "sale.success.delete.message": "Vente supprimée avec succès", "sale.details.title": "<PERSON><PERSON><PERSON> de <PERSON> vente", "sale.detail.customer.info": "Informations concernant le client", "sale.detail.invoice.info": "Informations sur la facture", "sales-return.title": "Retours de vente", "sale-return.title": "<PERSON><PERSON> de vente", "sale-return.create.title": "<PERSON><PERSON><PERSON> un retour de vente", "sale-return.edit.title": "Modifier le retour de la vente", "sale-return.success.create.message": "Retour de vente créé avec succès", "sale-return.success.edit.message": "Retour vente mis à jour avec succès", "sale-return.success.delete.message": "Retour de vente supprimé avec succès", "sale-return.details.title": "<PERSON>é<PERSON> du retour de la vente", "date-picker.filter.today.label": "<PERSON><PERSON><PERSON>'hui", "date-picker.filter.this-week.label": "<PERSON><PERSON> se<PERSON>", "date-picker.filter.last-week.label": "La semaine dernière", "date-picker.filter.this-month.label": "Ce mois-ci", "date-picker.filter.last-month.label": "Le mois dernier", "date-picker.filter.Custom-Range.label": "<PERSON><PERSON><PERSON>", "date-picker.filter.reset.label": "Réinitialiser", "date-picker.filter.apply.label": "Appliquer", "date-picker.filter.placeholder.label": "Sélectionner une date", "bar.title": "Bar", "line.title": "Ligne", "filter.label": "Filtre", "reports.title": "Signaler", "warehouse.reports.title": "Rapport d'en<PERSON>pôt", "sale.reports.title": "Rapport de vente", "stock.reports.title": "Rapport d'inventaire", "purchase.reports.title": "Rapport d'achat", "top-selling-product.reports.title": "Rapport sur les produits les plus vendus", "globally.react-table.column.code.label": "Code", "print.barcode.title": "Imprimer le code-barres", "paper.size.title": "<PERSON><PERSON>", "paper.size.placeholder.label": "Choisissez la taille du papier", "globally.paper.size.validate.label": "Veuillez sélectionner la taille du papier", "print.validate.label": "Veuillez mettre à jour le code-barres pour imprimer", "current.stock.label": "Stock actuel", "stock.report.details.title": "Détails du rapport de stock", "print.title": "<PERSON><PERSON><PERSON><PERSON>", "update.title": "Mise à jour", "preview.title": "<PERSON><PERSON><PERSON><PERSON>", "toast.successful.title": "Couronné <PERSON>", "toast.error.title": "<PERSON><PERSON><PERSON> chose s'est mal passé!", "unit.filter.all.label": "<PERSON>ut", "unit.filter.piece.label": "<PERSON><PERSON><PERSON>", "unit.filter.meter.label": "<PERSON><PERSON><PERSON>", "unit.filter.kilogram.label": "Kilogramme", "status.filter.received.label": "<PERSON><PERSON><PERSON>", "status.filter.pending.label": "En attente", "status.filter.ordered.label": "Commandé", "payment-status.filter.paid.label": "<PERSON><PERSON>", "payment-status.filter.unpaid.label": "Non payé", "excel.btn.label": "EXCELLER", "pdf.btn.label": "PDF", "cash.label": "En espèces", "no-option.label": "Aucune option", "warehouse.details.title": "d<PERSON><PERSON> de l'entrepôt", "select.payment-type.label": "type de paiement", "payment-type.filter.cheque.label": "Chèque", "payment-type.filter.bank-transfer.label": "Virement", "payment-type.filter.other.label": "<PERSON><PERSON>", "paying-amount-title": "Montant à payer", "create-payment-title": "C<PERSON>er un paiement", "reference-placeholder-label": "Entrez la référence", "input-Amount-to-pay-title": "Montant à payer", "edit-payment-title": "Modifier le paiement", "no-product-found.label": "Aucun produit trouvé", "sale.select.payment-type.placeholder": "Choisissez le type de paiement", "globally.payment.type.validate.label": "Veuillez sélectionner le type de paiement", "product.quantity.alert.reports.title": "Alertes de quantité de produit", "globally-saving-btn-label": "Économie...", "payment-status.filter.partial.label": "Partiel", "dashboard.recentSales.total-product.label": "Produits totaux", "adjustments.title": "Ajustements", "adjustments.create.title": "<PERSON><PERSON><PERSON> un ajustement", "adjustments.edit.title": "Modifier l'ajustement", "adjustments.detail.title": "Détails de l'ajustement", "Adjustment.success.create.message": "Ajustement créé avec succès", "Adjustment.success.edit.message": "Ajustement mis à jour avec succès", "Adjustment.success.delete.message": "Ajustement supprimé avec succès", "login-form.go-to-sign-in.label": "Retour à la connexion", "pos-product.title": "PRODUIT", "pos-qty.title": "QTÉ", "pos-price.title": "LE PRIX", "pos-sub-total.title": "SOUS-TOTAL", "pos-total-qty.title": "Quantité totale", "pos-total.title": "Total", "pos-pay-now.btn": "<PERSON>ez maintenant", "pos-globally.search.field.label": "Scanner/rechercher un produit par nom de code", "pos-make-Payment.title": "Effectuer le paiement", "pos-received-amount.title": "<PERSON><PERSON>", "pos-total-amount.title": "Montant total", "pos-sale.detail.invoice.info": "Facture d'achat", "pos-sale.detail.Phone.info": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos-sale.detail.Paid-bt.title": "Payé par", "pos-close-btn.title": "proche", "pos-item.print.invoice.title": "Article", "pos-all.categories.label": "toutes catégories", "pos-all.brands.label": "Toutes les marques", "pos-no-product-available.label": "Aucun produit disponible", "pos-sale.select.discount-type.placeholder": "Choisissez le type de remise", "pos-sale.select.sale-unit-type.placeholder": "Choisissez le type d'unité de vente", "pos-thank.you-slip.invoice": "<PERSON><PERSON><PERSON> de ma<PERSON>iner avec nous. V<PERSON><PERSON>z visiter à nouveau", "pos.payment.success.message": "Paiement effectué avec succès", "pos.subtotal.small.title": "Sous-total", "pos.cash-payment.product-error.message": "Veuillez ajouter le produit au panier", "pos.cash-payment.quantity-error.message": "Veuillez ajouter la quantité de produit", "pos.cash-payment.tax-error.message": "Veuillez saisir une valeur fiscale entre 0 et 100", "pos.cash-payment.total-amount-error.message": "Le montant de la remise ne doit pas être supérieur au total", "settings.system-settings.select.default-version-footer.placeholder.label": "Afficher le numéro de version dans le pied de page", "settings.system-settings.select.logo.placeholder.label": "Afficher le logo sur le bulletin de versement", "settings.system-settings.select.appname-sidebar.placeholder.label": "Afficher le nom de l'application dans la barre latérale", "pos.cash-payment.sub-total-amount-error.message": "Le montant de l'expédition ne doit pas être supérieur au sous-total", "product.import.title": "Importer des produits", "globally.sample.download.label": "Télécharger l'échantillon", "product-code.import.required-highlight.message": "le code ne doit pas exister déjà", "product-unit.import.required-highlight.message": "l'unité doit déjà être créée Veuillez utiliser le nom complet de l'unité", "globally.optional-input.validate.label": "<PERSON>mp facultatif", "reset.title": "Réinitialiser", "reset.yes.title": "<PERSON><PERSON>, réinitialiser", "reset.modal.msg": "Voulez-vous vraiment réinitialiser", "dashboard.widget.today-total-purchases.label": "Total des achats aujourd'hui", "dashboard.widget.today-payment-received.label": "Total reçu aujourd'hui (ventes)", "dashboard.widget.today-total-sales.label": "Ventes totales aujourd'hui", "dashboard.widget.today-total-expense.label": "Dépense totale aujourd'hui", "globally.file.validate.label": "Veuillez sélectionner le fichier", "globally.csv-file.validate.label": "Veuillez sélectionner le fichier csv", "file.success.upload.message": "<PERSON><PERSON><PERSON> téléchargé avec succès", "transfers.title": "Transferts", "transfer.title": "<PERSON><PERSON><PERSON><PERSON>", "transfer.create.title": "<PERSON><PERSON><PERSON> un transfert", "transfer.edit.title": "Modifier le transfert", "transfer.from-warehouse.title": "De l'entrepôt", "transfer.to-warehouse.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transfer.success.create.message": "Transfert créé avec succès", "transfer.success.edit.message": "Transfert mis à jour avec succès", "transfer.success.delete.message": "Transfert supprimé avec succès", "transfer.select.warehouse.validate.message": "Vous ne pouvez pas transférer de stock dans le même entrepôt", "status.filter.complated.label": "Complété", "status.filter.sent.label": "Expédié", "settings.prefixes-settings.input.purchases.placeholder.label": "PU", "settings.prefixes-settings.input.purchases-return.placeholder.label": "PR", "settings.prefixes-settings.input.sales.placeholder.label": "SL", "settings.prefixes-settings.input.salse-return.placeholder.label": "SR", "settings.prefixes-settings.input.expense.placeholder.label": "EX", "settings.prefixes-settings.input.purchases.validate.label": "Veuillez entrer les achats", "settings.prefixes-settings.input.purchases-return.validate.label": "Veuillez entrer le retour des achats", "settings.prefixes-settings.input.sales.validate.label": "Veu<PERSON>z entrer les ventes", "settings.prefixes-settings.input.salse-return.validate.label": "Veuillez saisir le retour sur ventes", "settings.prefixes-settings.input.expense.validate.label": "Veuillez entrer les dépenses", "product.export.title": "Produits d'exportation", "globally.input.content.label": "Contenu", "email-template.edit.title": "Modifier le modèle d'e-mail", "email-template.title": "Modèles de courrier électronique", "email-template.success.edit.message": "Les modèles d'e-mail ont été mis à jour avec succès.", "side-menu.empty.message": "Aucun enregistrements correspondants trouvés", "transfer.details.title": "<PERSON><PERSON><PERSON> du <PERSON>", "setting.state.lable": "État", "setting.postCode.lable": "Code Postal", "settings.system-settings.select.state.validate.label": "Veuillez sélectionner l'état", "settings.system-settings.select.country.validate.label": "Veuillez sélectionner le pays", "settings.system-settings.select.postcode.validate.label": "Veuillez sélectionner le code postal", "purchases.total.amount.title": "Montant total des achats", "purchases-return.total.amount.title": "Montant total des retours d'achats", "supplier.report.details.title": "Détails du rapport fournisseur", "supplier.report.title": "Rapport sur les fournisseurs", "prefix.title": "Préfixes", "quotations.title": "Citations", "create-quotation.title": "<PERSON><PERSON>er un devis", "edit-quotation.title": "Modifier le devis", "details-quotations.title": "Détails des devis", "quotation.success.create.message": "<PERSON><PERSON> c<PERSON> avec succès", "quotation.success.edit.message": "Devis mis à jour avec succès", "quotation.success.delete.message": "Devis supprimé avec succès", "settings.system-settings.select.date-format.label": "Format de données", "quotation.title": "<PERSON><PERSON>", "quotation.detail.invoice.info": "Informations sur le devis", "pepole.title": "Peuples", "converted.status.label": "<PERSON><PERSON><PERSON>", "settings.system-settings.select.postcode.validate.length.label": "La longueur du code postal ne doit pas dépasser 8", "profit-loss.reports.title": "Perte de profit,", "global.revenue.title": "<PERSON><PERSON><PERSON>", "global.gross-profit.title": "Bénéfice brut", "global.payment-received.title": "Pa<PERSON><PERSON> reçu", "global.payment-sent.title": "Paiements envoyés", "global.net-payment.title": "Paiements nets", "sms-template.edit.title": "Modifier le modèle de SMS", "sms-template.title": "<PERSON>d<PERSON><PERSON> de SMS", "sms-template.success.edit.message": "Modèle de SMS mis à jour avec succès.", "sms-content-variables.title": "VARIABLES DE CONTENU SMS", "email-content-variables.title": "VARIABLES DE CONTENU DES E-MAILS", "sms-content-text.error.message": "Vous avez atteint le nombre maximal de caractères autorisé 160.", "sms-content.error.message": "le contenu doit être obligatoire", "best-customer.report.title": "Meilleurs clients", "customer.report.details.title": "Détails du rapport client", "customer.report.title": "Rapports clients", "sale.payment.report.title": "Paiement de la vente", "sale.total.amount.title": "Montant total de la vente", "sale-return.total.amount.title": "Montant total du retour sur vente", "sale-Due.total.amount.title": "Montant total dû", "sale-paid.total.amount.title": "Montant total payé", "sale-reference.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> de <PERSON>e", "more-report.option.title": "Suite", "mail-settings.sender-name.title": "Nom de l'expéditeur", "mail-settings.title": "Paramètres de messagerie", "mail-settings.success.edit.message": "Paramètres de messagerie mis à jour avec succès", "pos.register.payment-method.": "Mode de paiement", "pos.register-details.sell.title": "vendre", "currency.icon.right.side.lable": "Icône de devise Côté droit", "total.sales.title": "Ventes totales", "email.status.edit.success.message": "Le statut de l'e-mail a été mis à jour avec succès.", "sms.status.edit.success.message": "Statut SMS mis à jour avec succès.", "sms-api.title": "SMS API", "key.lable": "Clé", "key.value.lable": "Valeur clé", "url.lable": "URL", "mobile.key.lable": "Clé mobile", "message.key.lable": "Clé des messages", "sms.status.lable": "Statut SMS", "active.status.lable": "Actif", "in-active.status.lable": "Inactif", "sms.api.update.success.message": "Mise à jour de l'API SMS Avec succès.", "sale-return.product-qty.validate.message": "La quantité retournée est supérieure à la quantité vendue", "template.title": "<PERSON><PERSON><PERSON><PERSON>", "pos.product-quantity-error.message": "Plus de quantité disponible.", "product-list.lable": "Liste de produits", "register.details.title": "Détails du registre", "register.total-sales.label": "Ventes totales", "register.total-refund.title": "Remboursement total", "register.total-payment.title": "Paiement total", "register.product.sold.title": "Détails des produits vendus", "register.product.sold.by.brand.title": "Détails des produits vendus (par marque)", "print-barcode.show-company.label": "<PERSON><PERSON><PERSON><PERSON> le nom du magasin", "print-barcode.show-product-name.label": "Affiche<PERSON> le nom du produit", "print-barcode.show-price.label": "<PERSON>ff<PERSON>r le prix", "product.input.quantity-limitation.label": "Limite de quantité", "product.input.quantity-limitation.placeholder": "Saisir la limite de quantité", "pos.hold-list-btn.title": "Tenir", "create.hold-list.warning.message": "Retenir la facture? La même référence remplacera l'ancienne liste si elle existe !!", "create-modal.yes.ok-btn": "<PERSON><PERSON> ok", "hold-list.reference-number.placeholder": "Veuillez entrer le numéro de référence!", "hold-list-id.table.column.label": "ID", "hold-list-ref-id.table.column.label": "Ref.ID", "hold-list.details.title": "Liste des réservations", "hold-list.success.create.message": "Liste de conservation créée avec succès", "hold-list.success.delete.message": "La liste bloquée a été supprimée avec succès", "report-all.warehouse.label": "<PERSON><PERSON>", "setting.mail-mailer.lable": "ENVOYEUR DE COURRIER", "setting.mail-host.lable": "HÉBERGEUR DE MESSAGERIE", "setting.mail-port.lable": "PORTE COURRIEL", "setting.mail-user-name.lable": "NOM D'UTILISATEUR DE COURRIEL", "setting.mail-password.lable": "MOT DE PASSE MESSAGERIE", "setting.mail-encryption.lable": "CHIFFREMENT DU COURRIER", "sale.payment.create.success": "Paiement de vente créé avec succès", "sale.payment.edit.success": "Le paiement de la vente a été mis à jour avec succès", "paying-amount-validate-label": "Le montant à payer doit être inférieur ou égal au montant à payer", "hold-list.reference-code.error": "Le champ code de référence est obligatoire.", "settings.clear-cache.success.message": "cache effacé avec succès", "product.product-in-stock.label": "En stock", "product-items.label": "Articles", "Payload.key.lable": "Charge utile", "base-units.title": "Unités de base", "base-unit.create.title": "Créer une unité de base", "base-unit.edit.title": "Modifier l'unité de base", "base-unit.title": "Unité de base", "base-unit.success.create.message": "Unité de base créée avec succès", "base-unit.success.edit.message": "Unité de base mise à jour avec succès", "base-unit.success.delete.message": "Unité de base supprimée avec succès", "DOB.input.label": "Date de naissance", "purchase.product.quantity.validate.label": "Veuillez entrer la quantité de produit", "languages.title": "Langages", "react-data-table.translation.column.label": "Traduction", "react-data-table.iso-date.column.label": "Norme ISO", "globally.input.iso-code.validate.label": "Veuillez entrer le code ISO", "globally.input.iso-code.character.validate.label": "La longueur du code ISO doit être égale à 2", "translation.manager.title": "Responsable de la traduction", "language.updated.success.message": "<PERSON>ue mise à jour avec succès", "language.deleted.success.message": "Langue supprimée avec succès", "language.edit.success.message": "Langue modifiée avec succès", "language.save.success.message": "Langue créée avec succès", "language.enabled.success.message": "Langue activée avec succès", "language.disabled.success.message": "Langue désactivée avec succès", "language.current-language-disable.error.message": "Vous ne pouvez pas désactiver votre langue actuellement sélectionnée", "header.profile-menu.change-language.label": "Changer de langue", "language.title": "<PERSON><PERSON>", "pos-paying-amount.title": "Montant à payer", "pos.change-return.label": "Modifier le retour", "language.create.title": "<PERSON><PERSON><PERSON> une langue", "purchase.less.recieving.ammout.error": "Le montant reçu est inférieur au total général.", "add-stock.title": "Ajouter des actions", "product-quantity.add.title": "Ajouter la quantité de produit", "edit-translation.title": "Modifier la traduction", "globally.input.cash-in-hand.label": "Encaisse", "globally.close-register.title": "<PERSON><PERSON><PERSON> le registre", "register.closed.successfully.message": "Enregistrement fermé avec succès.", "register.entry.added.successfully.message": "L'entrée de registre a été ajoutée avec succès.", "globally.total-cash.label": "Encaisse totale", "globally.input.note.label": "<PERSON><PERSON><PERSON>", "globally.input.note.placeholder.label": "<PERSON><PERSON> une note", "register.report.title": "Enregistrer le rapport", "user-details.table.opened-on.row.label": "Ouvert le", "user-details.table.closde-on.row.label": "<PERSON><PERSON><PERSON> le", "globally.input.cash-in-hand-while-closing.label": "Encaisse pendant la fermeture", "pos.cclose-register.enter-total-cash.message": "Veuillez ajouter le total en espèces.", "register.is.still.open.message": "Le registre est toujours ouvert !!!", "Are.you.sure.you.want.to.go.to.dashboard.message": "Êtes-vous sûr de vouloir accéder au tableau de bord ?", "product.quantity.title": "Quantité du produit", "pos.this.product.out.of.stock.message": "Ce produit est en rupture de stock", "pos.quantity.exceeds.quantity.available.in.stock.message": "La quantité dépasse la quantité disponible en stock", "yes.modal.title": "O<PERSON>", "no.modal.title": "Non", "language.edit.title": "Modifier la langue", "select.user.label": "Sélectionnez un utilisateur", "suppliers.import.title": "Importer des fournisseurs", "customers.import.title": "Importer des clients", "products.type.single-type.label": "Unique", "product.type.label": "Type de produit", "product.type.placeholder.label": "Choisir le type de produit", "product.type.input.validation.error": "Veuillez sélectionner le type de produit", "variation.name": "Nom de la variation", "variation.variation_types": "Types de variation", "variations.title": "Variations", "variation.title": "Variation", "variation.create.title": "<PERSON><PERSON><PERSON> une variation", "variation.edit.title": "Modifier la variation", "variation.input.name.label": "Nom", "variation.input.name.placeholder.label": "Entrez un nom", "variation.input.name.validate.label": "Veuillez saisir un nom", "variation.success.create.message": "Variation créée avec succès", "variation.success.edit.message": "Variation modifiée avec succès", "variation.success.delete.message": "Variation supprimée avec succès", "variation.types.title": "Types de variation", "variation.type.title": "Type de variation", "variation.type.input.name.placeholder.label": "Veuillez saisir un type de variation", "variation.type.input.name.validate.label": "Veuillez saisir des types de variation valides", "variation.select.validation.error.message": "Veuillez sélectionner une variation", "variation.type.select.validate.error.message": "Veuillez sélectionner des types de variation", "products.warehouse.title": "Entrepôt de produits", "barcode-symbol-uppercase-validation-message": "Veuillez saisir uniquement des lettres majuscules et des chiffres pour le code-barres Code 39.", "sale.product-qty.limit.validate.message": "Vous ne pouvez pas acheter plus que la quantité limite", "receipt-settings.title": "Paramètres du reçu", "receipt-settings.show-warehouse.label": "Afficher l'entrepôt", "receipt-settings.show-email.label": "Afficher l'e-mail", "receipt-settings.show-address.label": "Afficher l'adresse", "receipt-settings.show-customer.label": "<PERSON><PERSON><PERSON><PERSON> le <PERSON>", "receipt-settings.show-phone.label": "Aff<PERSON>r le téléphone", "receipt-settings.show-discount-shipping.label": "Afficher la remise et les frais de livraison", "receipt-settings.success.edit.message": "Paramètre du reçu mis à jour avec succès", "receipt-settigns.input.note.validate.label": "V<PERSON><PERSON>z saisir une note", "receipt-settings.show-barcode.label": "Afficher le code-barres sur le reçu", "receipt-settings.show-note.label": "Affiche<PERSON> la note", "globally.submit-and-print-button": "Soumettre et imprimer", "receipt-settings.show-product-code.label": "Afficher le code produit", "globally.footer.label": "Tous droits réservés", "logout.confirmation.label": "Êtes-vous sûr de vouloir vous déconnecter ?", "addition.title": "Addition", "subtraction.title": "Soustraction", "print-custom-barcode.title": "Imprimer un code-barres personnalisé", "product.sku.label": "SKU/Code-barres", "add.stock.while.product.creation.title": "Ajouter du stock lors de la création du produit", "confirm-modal.msg": "Êtes-vous sûr ?", "store.title": "Ma<PERSON>ins", "create.store.title": "<PERSON><PERSON><PERSON> un magasin", "edit.store.title": "Modifier le magasin", "store.name.title": "Nom du magasin", "store.name.placeholder.title": "Saisis<PERSON>z le nom du magasin", "store.name.validate.label": "Veuillez saisir le nom du magasin", "store.success.create.message": "<PERSON><PERSON><PERSON>", "store.success.edit.message": "<PERSON><PERSON><PERSON> mis à jour", "store.success.delete.message": "<PERSON><PERSON><PERSON> supprimé", "store.changed.message": "<PERSON><PERSON><PERSON> modifi<PERSON>", "store.header.name.title": "Ma<PERSON><PERSON>", "select.all.store.title": "Sélectionner tous les magasins", "store.field.must.required.validate": "Le champ magasin doit être obligatoire", "store.assigned.title": "Magasins attribués", "no.store.title": "Aucune information sur le magasin", "paid.amount.title": "<PERSON><PERSON> payé", "taxes.title": "Taxes", "tax.title": "Taxe", "add.tax.title": "Ajouter une taxe", "edit.tax.title": "Modifier la taxe", "tax.name.title": "Nom de la taxe", "tax.name.placeholder.title": "Saisir le nom de la taxe", "tax.name.validate.title": "Veuillez saisir le nom de la taxe", "tax.value.title": "Valeur de la taxe", "tax.value.placeholder.title": "Sai<PERSON> la valeur de la taxe", "tax.value.validate.title": "Veuillez saisir la valeur de la taxe", "tax.deleted.success.message": "Taxe supprimée", "tax.edit.success.message": "Taxe modifiée", "tax.save.success.message": "Taxe créée", "tax.name.unique.validate.title": "Le nom de la taxe existe déjà", "tax.value.unique.validate.title": "La valeur de la taxe existe déjà", "tax.show.on.receipt.pdf.title": "Afficher sur le reçu/PDF", "pos.settings.title": "Paramètres du PDV", "enable.pos.sound.title": "<PERSON><PERSON> le son du clic du PDV", "show.out.of.stock.product.in.pos": "Afficher les produits en rupture de stock dans le PDV", "pos.sound.title": "Son PDV", "upload.audio.title": "Télécharger l'audio", "pos.audio.required": "Veuillez télécharger l'audio", "date.of.birth.title": "Date de naissance", "customer.details.title": "Informations sur le client", "pos.audio.length.tooltip.title": "La durée de l'audio doit être inférieure à 3 secondes.", "select.date.of.birth": "Sélectionnez votre date de naissance", "item.deleted.success.message": "Élément supprimé avec succès", "payment.method.save.success.message": "Mode de paiement créé", "payment.method.edit.success.message": "Mode de paiement modifié", "payment.method.deleted.success.message": "Mode de paiement supprimé", "payment.method.name.unique.validate.title": "Le nom du mode de paiement existe déjà", "show.tax.title": "Afficher la taxe", "expiry.date.title": "Date d'expiration", "expiry.date.placeholder.title": "<PERSON><PERSON> la date d'expiration", "payment.method.title": "Mode de paiement", "dual.screen.settings.title": "Paramètres du double écran", "dual.screen.display.header.title": "<PERSON>ff<PERSON><PERSON> l'en-tête", "dual.screen.display.header.placeholder.title": "<PERSON><PERSON> l'en-tête", "please.enter.display.header.title": "Veuillez saisir l'en-tête", "carousel.image.title": "Images du carrousel", "validation.you.can.upload.maximum.images": "Vous pouvez télécharger un maximum de 5 images", "upload.maximum.images": "Télécharger un maximum de cinq images.", "no.customer.selected.title": "Aucun client sélectionné", "send.test.email.title": "Envoyer un e-mail de test", "send.test.email.success.message": "E-mail de test envoy<PERSON> avec succès", "globally.receipt.download.label": "Télécharger le reçu", "load.more.title": "Charger plus", "pos.edit.sale.title": "Êtes-vous sûr de vouloir modifier cette vente ?", "sale.payment.total-exceed.validate.message": "Le montant total du paiement ne doit pas dépasser le total général.", "pos.payment.amount.exceeds.total.error": "Le montant du paiement ne peut pas dépasser le montant total", "pos.payment.total.exceeds.grand.total.error": "Le montant total du paiement dépasse le total général", "globally.payment.details.validate.label": "Veuillez vérifier les détails du paiement pour détecter d'éventuelles erreurs", "globally.detail.payment.details": "Détails du paiement", "globally.input.width.label": "<PERSON><PERSON>", "globally.input.height.label": "<PERSON><PERSON>", "globally.input.width.validate.label": "Veuillez saisir la largeur", "globally.input.height.validate.label": "Veuillez saisir la hauteur"}