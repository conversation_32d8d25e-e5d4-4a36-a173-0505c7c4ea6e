{"dashboard.title": "Instrumententafel", "header.pos.title": "Pos", "header.profile-menu.profile.label": "Profil", "header.profile-menu.change-password.label": "Ändere das Passwort", "header.profile-menu.logout.label": "Ausloggen", "product.categories.title": "Produktkategorien", "expense.categories.title": "Ausgabenkategorien", "dashboard.salesReturn.title": "Verkaufsrückgabe", "dashboard.top-customers.title": "Top 5 Kunden", "dashboard.purchaseReturn.title": "Einkäufe Rückgabe", "dashboard.ThisWeekSales&Purchases.title": "Diese Woche Verkäufe & Käufe", "dashboard.TopSellingProducts.title": "Meistverkaufte Produkte", "dashboard.stockAlert.title": "Lageralarm", "dashboard.recentSales.title": "Aktuelle Verkäufe", "dashboard.PaymentSentReceived.title": "Zahlung gesendet und erhalten", "dashboard.stockAlert.code.label": "Code", "dashboard.stockAlert.product.label": "Produkt", "dashboard.stockAlert.warehouse.label": "Lagerhaus", "dashboard.stockAlert.quantity.label": "<PERSON><PERSON>", "dashboard.stockAlert.alertQuantity.label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dashboard.grantTotal.label": "Gesamtsumme", "dashboard.recentSales.reference.label": "<PERSON><PERSON><PERSON><PERSON>", "dashboard.recentSales.customer.label": "Kunde", "dashboard.recentSales.status.label": "Status", "dashboard.recentSales.paid.label": "Be<PERSON>hlt", "dashboard.recentSales.due.label": "<PERSON><PERSON><PERSON><PERSON>", "dashboard.recentSales.paymentStatus.label": "Zahlungsstatus", "settings.select.language.label": "<PERSON><PERSON><PERSON>", "settings.select.language.placeholder": "Sprache Auswählen", "users.title": "<PERSON><PERSON><PERSON>", "user.create.title": "<PERSON><PERSON><PERSON> er<PERSON>", "user.edit.title": "<PERSON><PERSON><PERSON> bearbeiten", "user.input.first-name.label": "<PERSON><PERSON><PERSON>", "user.input.last-name.label": "Familienname, Nachname", "user.input.email.label": "Email", "user.input.phone-number.label": "Telefonnummer", "user.input.password.label": "Passwort", "user.input.confirm-password.label": "Bestätige das Passwort", "user.input.role.label": "<PERSON><PERSON>", "user.input.first-name.placeholder.label": "Bitte Vornamen eingeben", "user.input.last-name.placeholder.label": "<PERSON><PERSON>namen e<PERSON>ben", "user.input.email.placeholder.label": "<PERSON><PERSON>", "user.input.phone-number.placeholder.label": "Telefonnummer eingeben", "user.input.password.placeholder.label": "Passwort eingeben", "user.input.confirm-password.placeholder.label": "Geben Sie das Kennwort bestätigen ein", "user.input.role.placeholder.label": "Wählen Sie Rolle", "users.table.user.column.title": "<PERSON><PERSON><PERSON>", "users.table.phone-number.column.title": "Telefonnummer", "users.table.role.column.title": "<PERSON><PERSON>", "users.table.date.column.title": "Erstellungsdatum", "user-details.title": "Nutzerdetails", "user-details.table.created-on.row.label": "Erstellt am", "user-details.table.title": "Überblick", "user.input.first-name.validate.label": "<PERSON>te geben Si<PERSON> den Vornamen ein", "user.input.last-name.validate.label": "<PERSON>te geben Si<PERSON> den Nachnamen ein", "user.input.email.validate.label": "Bitte E-Mail-Ad<PERSON><PERSON> e<PERSON>ben", "user.input.email.valid.validate.label": "Bitte geben Si<PERSON> eine gültige Email Adresse an", "user.input.phone-number.validate.label": "Bitte Telefonnummer eingeben", "user.input.password.validate.label": "Bitte Passwort eingeben", "user.input.confirm-password.validate.label": "Bitte bestätigen Sie das Passwort", "user.input.role.validate.label": "Bitte Rolle auswählen", "user.success.create.message": "Benutzer erfolgreich erstellt", "user.success.edit.message": "Benutzer erfolgreich aktualisiert", "user.success.delete.message": "Benutzer erfolgreich <PERSON>", "react-data-table.searchbar.placeholder": "<PERSON><PERSON>", "react-data-table.action.column.label": "Handlung", "react-data-table.date.column.label": "Datum", "react-data-table.no-record-found.label": "Es sind keine Datensätze zum Anzeigen vorhanden", "react-data-table.records-per-page.label": "Einträge pro Seite", "dataTable.searchBar.placeholder.label": "<PERSON><PERSON>", "delete-modal.title": "Löschen!", "delete-modal.msg": "<PERSON><PERSON>cht<PERSON> Si<PERSON> dies wirklich löschen?", "delete-modal.yes-btn": "Ja, l<PERSON><PERSON>!", "delete-modal.no-btn": "<PERSON><PERSON>, Abbrechen", "suppliers.title": "Lieferanten", "supplier.title": "<PERSON><PERSON><PERSON>", "supplier.create.title": "<PERSON><PERSON><PERSON> anlegen", "supplier.edit.title": "Lieferant bearbeiten", "supplier.table.name.column.title": "Name", "supplier.table.phone-number.column.title": "Telefonnummer", "supplier.table.email.column.title": "Email", "supplier.table.address.column.title": "Die Anschrift", "supplier.success.create.message": "Lieferant erfolgreich erstellt", "supplier.success.edit.message": "Lieferant erfolgreich aktualisiert", "supplier.success.delete.message": "Lieferant erfolg<PERSON><PERSON>", "globally.input.name.label": "Name", "globally.input.name.placeholder.label": "Name e<PERSON>ben", "globally.react-table.column.created-date.label": "Erstellt am", "globally.react-table.column.payment-type.label": "Zahlungsart", "globally.input.email.label": "Email", "globally.input.email.placeholder.label": "<PERSON><PERSON>", "globally.input.phone-number.label": "Telefonnummer", "globally.input.phone-number.placeholder.label": "Telefonnummer eingeben", "globally.input.country.label": "Land", "globally.input.country.placeholder.label": "Land eingeben", "globally.input.city.label": "Stadt", "globally.input.city.placeholder.label": "Stadt betreten", "globally.input.address.label": "Die Anschrift", "globally.input.address.placeholder.label": "<PERSON><PERSON><PERSON> e<PERSON>", "globally.input.notes.label": "Anmerkungen", "globally.input.notes.placeholder.label": "<PERSON><PERSON><PERSON> Sie Notizen ein", "globally.loading.label": "<PERSON>ten <PERSON> mal...", "globally.input.name.validate.label": "Bitte Namen e<PERSON>ben", "globally.input.email.validate.label": "Bitte E-Mail-Ad<PERSON><PERSON> e<PERSON>ben", "globally.input.email.valid.validate.label": "Bitte geben Si<PERSON> eine gültige Email Adresse an", "globally.input.country.validate.label": "Bitte Land eingeben", "globally.input.city.validate.label": "Bitte Stadt eingeben", "globally.input.phone-number.validate.label": "Bitte geben Sie die Telefonnummer ein", "globally.input.address.validate.label": "<PERSON><PERSON> Adresse e<PERSON>ben", "globally.input.notes.validate.label": "Die Anmerkungen dürfen nicht länger als 100 Zeichen sein", "globally.require-input.validate.label": "<PERSON><PERSON> wird <PERSON>", "globally.date.validate.label": "Bitte Datum e<PERSON>ben", "globally.tax-length.validate.label": "Die Steuer darf nicht größer als 100 sein", "globally.discount-length.validate.label": "<PERSON> Rabatt darf nicht größer als 100 sein", "globally.discount-cost-length.validate.label": "<PERSON> Rabatt darf nicht höher sein als die Produktkosten", "globally.discount-price-length.validate.label": "Der Rabatt darf den Produktpreis nicht übersteigen", "globally.status.validate.label": "Bitte Status auswählen", "globally.payment.status.validate.label": "Bitte Zahlungsstatus auswählen", "globally.type.label": "<PERSON><PERSON>", "globally.back-btn": "Zurück", "globally.save-btn": "Speichern", "globally.cancel-btn": "Absagen", "globally.edit-btn": "<PERSON><PERSON><PERSON>", "globally.submit-btn": "Einreichen", "globally.edit.tooltip.label": "<PERSON><PERSON><PERSON>", "globally.delete.tooltip.label": "Löschen", "globally.view.tooltip.label": "<PERSON><PERSON>", "globally.pdf.download.label": "PDF Herunterladen", "globally.product-quantity.validate.message": "Bitte Produktmenge hinzufügen", "globally.product-already-added.validate.message": "Dieses Produkt wurde bereits hinzugefügt", "globally.detail.reference": "<PERSON><PERSON><PERSON><PERSON>", "globally.detail.status": "Status", "globally.detail.warehouse": "Lagerhaus", "globally.detail.payment.status": "Zahlungsstatus", "globally.detail.company.info": "Firmeninfo", "globally.detail.order.summary": "Bestellübersicht", "globally.detail.product": "Produkt", "globally.detail.net-unit-cost": "Nettostückkosten", "globally.detail.net-unit-price": "Netto-Einheitspreis", "globally.detail.quantity": "<PERSON><PERSON>", "globally.detail.unit-cost": "Kosten pro Einheit", "globally.detail.unit-price": "Einzelpreis", "globally.detail.discount": "<PERSON><PERSON><PERSON>", "globally.detail.tax": "<PERSON><PERSON><PERSON>", "globally.detail.subtotal": "Zwischensumme", "globally.detail.order.tax": "<PERSON><PERSON><PERSON><PERSON>", "globally.show.payment.label": "Zahlungen anzeigen", "globally.detail.shipping": "<PERSON>ers<PERSON>", "globally.detail.grand.total": "Gesamtsumme", "globally.detail.paid": "Be<PERSON>hlt", "globally.detail.due": "<PERSON><PERSON><PERSON><PERSON>", "customers.title": "<PERSON><PERSON>", "customer.title": "Kunde", "globally.search.field.label": "Produkt nach Codename suchen", "customer.create.title": "<PERSON>nde anlegen", "customer.edit.title": "Kunde bearbeiten", "customer.success.create.message": "Kunde erfolgreich erstellt", "customer.success.edit.message": "Kunde erfolgreich aktualisiert", "customer.success.delete.message": "Kunde erfolgreich gelöscht", "warehouse.title": "Lagerhaus", "warehouse.create.title": "Lager erstellen", "warehouse.edit.title": "Lager bearbeiten", "warehouse.input.zip-code.label": "<PERSON><PERSON><PERSON><PERSON>", "warehouse.input.zip-code.placeholder.label": "<PERSON><PERSON>itzahl eingeben", "warehouse.input.zip-code.validate.label": "<PERSON><PERSON> e<PERSON>ben", "warehouse.input.zip-code.valid.validate.label": "<PERSON>te geben Si<PERSON> eine gültige Postleitzahl ein", "warehouse.success.create.message": "Lager erfolgreich erstellt", "warehouse.success.edit.message": "Lager erfolgreich aktualisiert", "warehouse.success.delete.message": "Lager erfolgreich <PERSON>", "products.title": "Produkte", "product.title": "Produkt", "product.create.title": "Produkt erstellen", "product.edit.title": "Produkt bearbeiten", "product.input.code.label": "Code", "product.input.code.placeholder.label": "Code eingeben", "product.input.product-category.label": "Produktkategorie", "product.input.product-category.placeholder.label": "Wählen Sie Produktkategorie", "product.input.brand.label": "<PERSON><PERSON>", "product.input.brand.placeholder.label": "Wählen Sie Marke", "product.input.barcode-symbology.label": "Barcode-Symbologie", "product.input.barcode-symbology.placeholder.label": "Wählen Sie Barcode-Symbologie", "product.input.product-cost.label": "Produktkosten", "product.input.product-cost.placeholder.label": "Produktkosten eingeben", "product.input.product-price.label": "Produktpreis", "product.input.product-price.placeholder.label": "Geben Sie den Produktpreis ein", "product.input.product-unit.label": "Produkteinheit", "product.input.product-unit.placeholder.label": "Wählen Sie Produkteinheit", "product.input.sale-unit.label": "Verkaufseinheit", "product.input.sale-unit.placeholder.label": "Wählen Sie Verkaufseinheit", "product.input.purchase-unit.label": "Kaufeinheit", "product.input.purchase-unit.placeholder.label": "Wählen Sie Einkaufseinheit", "product.input.stock-alert.label": "Lageralarm", "product.input.order-tax.label": "<PERSON><PERSON><PERSON><PERSON>", "product.input.order-tax.placeholder.label": "Bestellsteuer eingeben", "product.input.order-tax.validate.label": "<PERSON>te geben Si<PERSON> die Bestellsteuer ein", "product.input.order-tax.valid.validate.label": "Die Steuer darf nicht größer als 100 sein", "product.input.tax-type.label": "Steuerart", "tax-type.filter.exclusive.label": "Exklusiv", "tax-type.filter.inclusive.label": "Inklusive", "product.input.tax-type.placeholder.label": "Wählen Sie Steuerart", "product.input.warehouse.placeholder.label": "Wählen Sie Lager", "product.input.multiple-image.label": "Mehrfaches Bild", "product.table.image.column.label": "Bild", "product.table.price.column.label": "Pre<PERSON>", "product.product-details.title": "Produktdetails", "product.product-details.code-product.label": "Produktcode", "product.product-details.category.label": "<PERSON><PERSON><PERSON>", "product.product-details.cost.label": "<PERSON><PERSON>", "product.product-details.unit.label": "Einheit", "product.product-details.tax.label": "<PERSON><PERSON><PERSON>", "product.input.code.validate.label": "Bitte Code eingeben", "product.input.product-category.validate.label": "Bitte Produktkategorie auswählen", "product.input.brand.validate.label": "Bitte Marke auswählen", "product.input.barcode-symbology.validate.label": "Bitte Barcode-Symbologie auswählen", "product.input.product-cost.validate.label": "<PERSON>te geben Sie die Produktkosten ein", "product.input.product-price.validate.label": "Bitte Produktpreis eingeben", "product.input.product-unit.validate.label": "Bitte Produkteinheit auswählen", "product.input.sale-unit.validate.label": "Bitte Verkaufseinheit auswählen", "product.input.purchase-unit.validate.label": "Bitte Kaufeinheit auswählen", "product.input.stock-alert.validate.label": "Bitte Lageralarm eingeben", "product.input.tax-type.validate.label": "Bitte wählen Sie die Steuerart aus", "product.input.warehouse.validate.label": "Bitte Lager auswählen", "product.success.create.message": "Produkt erfolgreich erstellt", "product.success.edit.message": "Produkt erfolgreich aktualisiert", "product.success.delete.message": "Produkt erfolgreich gelöscht", "product.image.success.upload.message": "Bild erfolgreich hochgeladen", "product.image.success.delete.message": "Bild erfolgreich <PERSON>", "brands.title": "<PERSON><PERSON>", "brand.title": "<PERSON><PERSON>", "brand.create.title": "<PERSON><PERSON>", "brand.edit.title": "<PERSON><PERSON> bear<PERSON>", "brand.input.code.label": "Code", "globally.input.change-logo.tooltip": "Logo ändern", "globally.input.change-image.tooltip": "Bild ändern", "brand.table.brand-name.column.label": "Markenname", "brand.table.product-count.column.label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "brand.input.name.valid.validate.label": "Der Name darf nicht länger als 50 Zeichen sein", "brand.success.create.message": "<PERSON><PERSON> erfolgreich erstellt", "brand.success.edit.message": "Marke erfolgreich aktualisiert", "brand.success.delete.message": "<PERSON><PERSON> erfolg<PERSON><PERSON>", "product-categories.title": "Produktkategorien", "product-category.title": "Produktkategorie", "product-category.create.title": "Produktkategorie erstellen", "product-category.edit.title": "Produktkategorie bearbeiten", "product-category.success.create.message": "Produktkategorie Erfolgreich erstellt", "product-category.success.edit.message": "Produktkategorie erfolgreich aktualisiert", "product-category.success.delete.message": "Produktkategorie erfolgreich gelöscht", "expense-categories.title": "Ausgabenkategorien", "expense-category.title": "Ausgabenkategorie", "expense-category.create.title": "Ausgabenkategorie erstellen", "expense-category.edit.title": "Ausgabenkategorie bearbeiten", "expense-category.success.create.message": "Ausgabenkategorie erfolgreich erstellt", "expense-category.success.edit.message": "Ausgabenkategorie erfolgreich aktualisiert", "expense-category.success.delete.message": "Ausgabenkategorie erfolgreich gelöscht", "expenses.title": "<PERSON><PERSON>", "expense.title": "<PERSON><PERSON><PERSON><PERSON>", "expense.create.title": "<PERSON><PERSON><PERSON><PERSON>", "expense.edit.title": "Ausgaben bearbeiten", "expense.input.details.label": "Einzelheiten", "expense.input.details.placeholder.label": "Details eingeben", "expense.input.amount.label": "<PERSON><PERSON>", "expense.input.amount.placeholder.label": "<PERSON><PERSON> e<PERSON>ben", "expense.input.warehouse.placeholder.label": "Wählen Sie Lager", "expense.input.title.label": "Ausgabentitel", "expense.input.title.validate.label": "Bitte geben Sie den Ausgabentitel ein", "expense.input.title.placeholder.label": "Geben Sie den Ausgabentitel ein", "expense.input.expense-category.placeholder.label": "Wählen Sie Ausgabenkategorie", "expense.input.warehouse.validate.label": "Bitte Lager auswählen", "expense.input.expense-category.validate.label": "Bitte Ausgabenkategorie auswählen", "expense.input.amount.validate.label": "Bitte Betrag eingeben", "expense.success.create.message": "Spesen erfolgreich erstellt", "expense.success.edit.message": "Ausgaben erfolgreich aktualisiert", "expense.success.delete.message": "<PERSON><PERSON> erfolgreich <PERSON>", "roles.title": "<PERSON><PERSON>", "roles.permissions.title": "Rollen/Berechtigungen", "role.title": "<PERSON><PERSON>", "role.create.title": "<PERSON><PERSON> erstellen", "role.edit.title": "<PERSON><PERSON> bearbeiten", "role.select.all-permission.label": "Alle Berechtigungen", "role.input.permission.label": "Berechtigungen", "role.input.name.validate.label": "Bitte Namen e<PERSON>ben", "role.input.name.valid.validate.label": "Der Name darf nicht länger als 50 Zeichen sein", "role.success.create.message": "Rolle erfolgreich erstellt", "role.success.edit.message": "Rolle erfolgreich aktualisiert", "role.success.delete.message": "Rolle erfolgreich <PERSON>", "units.title": "Einheiten", "unit.title": "Einheit", "unit.create.title": "Einheit erstellen", "unit.edit.title": "Einheit bearbeiten", "unit.modal.input.short-name.label": "Kurzer Name", "unit.modal.input.short-name.placeholder.label": "Geben Sie den Kurznamen ein", "unit.modal.input.base-unit.label": "Grundeinheit", "unit.modal.input.base-unit.placeholder.label": "Wählen Sie Basiseinheit", "unit.modal.input.short-name.validate.label": "<PERSON>te geben Si<PERSON> einen Kurznamen ein", "unit.modal.input.short-name.valid.validate.label": "Der Kurzname darf nicht länger als 50 Zeichen sein", "unit.modal.input.base-unit.validate.label": "Bitte Grundgerät auswählen", "unit.success.create.message": "Einheit erfolgreich erstellt", "unit.success.edit.message": "Einheit erfolgreich aktualisiert", "unit.success.delete.message": "Einheit erfolgreich gelö<PERSON>t", "currencies.title": "Währungen", "currency.title": "Währung", "currency.create.title": "Währung erstellen", "currency.edit.title": "Währung bearbeiten", "currency.modal.input.name.placeholder.label": "Geben Sie den Währungsnamen ein", "currency.modal.input.code.label": "Code", "currency.modal.input.code.placeholder.label": "Geben Sie den Währungscode ein", "currency.modal.input.symbol.label": "Symbol", "currency.modal.input.symbol.placeholder.label": "Geben Sie das Währungssymbol ein", "currency.modal.input.name.validate.label": "Bitte geben Sie den Währungsnamen ein", "currency.modal.input.code.validate.label": "Bitte geben Sie den Währungscode ein", "currency.modal.input.code.valid.validate.label": "Der Code darf nicht länger als 20 Zeichen sein", "currency.modal.input.symbol.validate.label": "Bitte geben Sie das Währungssymbol ein", "currency.success.create.message": "Währung erfolgreich erstellt", "currency.success.edit.message": "Währung erfolgreich aktualisiert", "currency.success.delete.message": "Währung erfolgreich gelöscht", "purchases.title": "Einkäufe", "purchase.title": "<PERSON><PERSON><PERSON>", "purchase.create.title": "Einkauf er<PERSON>llen", "purchase.edit.title": "<PERSON><PERSON>", "purchase.select.warehouse.label": "Lagerhaus", "purchase.select.warehouse.placeholder.label": "Wählen Sie Lager", "purchase.select.supplier.label": "<PERSON><PERSON><PERSON>", "purchase.select.supplier.placeholder.label": "Wählen Sie Lieferant", "purchase.select.status.label": "Status", "purchase.select.status.placeholder.label": "Wählen Sie Status", "purchase.order-item.table.label": "Auftragspositionen", "purchase.order-item.table.net-unit-cost.column.label": "Nettostückkosten", "purchase.order-item.table.stock.column.label": "Aktie", "purchase.order-item.table.qty.column.label": "<PERSON><PERSON>", "purchase.order-item.table.discount.column.label": "<PERSON><PERSON><PERSON>", "purchase.order-item.table.tax.column.label": "<PERSON><PERSON><PERSON>", "purchase.order-item.table.sub-total.column.label": "Zwischensumme", "purchase.input.order-tax.label": "<PERSON><PERSON><PERSON><PERSON>", "purchase.input.shipping.label": "<PERSON>ers<PERSON>", "purchase.placeholder.notes.input": "<PERSON><PERSON><PERSON> Sie Notizen ein", "purchase.product-list.validate.message": "Bitte Produkt zur Liste hinzufügen", "purchase.select.warehouse.validate.label": "Bitte Lager auswählen", "purchase.select.supplier.validate.label": "Bitte Anbieter auswählen", "purchase.input.discount.validate.label": "<PERSON><PERSON> e<PERSON>", "purchase.input.order-tax.validate.label": "<PERSON>te geben Si<PERSON> die Bestellsteuer ein", "purchase.input.shipping.validate.label": "Bitte Versand eingeben", "purchase.product-modal.select.discount-type.label": "<PERSON><PERSON><PERSON><PERSON>", "discount-type.filter.percentage.label": "Prozentsatz", "discount-type.filter.fixed.label": "Fest", "purchase.table.column.reference-code.label": "Referenzcode", "purchase.grant-total.label": "Gesamtsumme", "purchase.success.create.message": "<PERSON><PERSON> er<PERSON><PERSON><PERSON><PERSON> erste<PERSON>t", "purchase.success.edit.message": "<PERSON><PERSON><PERSON><PERSON>g<PERSON>ich aktualisiert", "purchase.success.delete.message": "<PERSON><PERSON> er<PERSON><PERSON><PERSON><PERSON><PERSON>", "purchases.details.title": "Kaufdetails", "purchase.detail.supplier.info": "Lieferanteninfo", "purchase.detail.purchase.info": "<PERSON><PERSON><PERSON><PERSON>", "purchases.return.title": "Einkäufe Rückgaben", "purchase.return.create.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> er<PERSON>", "purchase.return.edit.title": "Kaufrückgabe bearbeiten", "purchase.return.success.create.message": "Kaufrücksendung erfolgreich erstellt", "purchase.return.success.edit.message": "Kaufrückgabe erfolgreich aktualisiert", "purchase.return.success.delete.message": "Kaufrückgabe erfolgreich gelöscht", "purchases.return.details.title": "Details zur Kaufrückgabe", "settings.title": "Einstellungen", "settings.system-settings.title": "Systemeinstellungen", "settings.system-settings.select.default-currency.label": "Standardwährung", "settings.system-settings.select.default-currency.placeholder.label": "Währung wählen", "settings.system-settings.input.default-email.label": "Standard-E-Mail", "settings.system-settings.input.default-email.placeholder.label": "<PERSON><PERSON>", "settings.system-settings.select.default-language.label": "Standardsprache", "settings.system-settings.select.default-language.placeholder.label": "Sprache wählen", "settings.system-settings.select.default-customer.label": "Standardkunde", "settings.system-settings.select.default-customer.placeholder.label": "Wählen Sie Kunde", "settings.system-settings.select.default-warehouse.label": "Standardlager", "settings.system-settings.select.default-warehouse.placeholder.label": "Wählen Sie Lager", "settings.system-settings.input.change-logo.label": "Logo ändern", "settings.system-settings.input.company-name.label": "Name der Firma", "settings.system-settings.input.company-name.placeholder.label": "<PERSON>eb<PERSON> Si<PERSON> den Firmennamen ein", "settings.system-settings.input.company-phone.label": "Firmentelefon", "settings.system-settings.input.company-phone.placeholder.label": "Geben Sie das Firmentelefon ein", "settings.system-settings.input.developed-by.label": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "settings.system-settings.input.developed-by.placeholder.label": "<PERSON><PERSON><PERSON> Si<PERSON> Entwickelt von ein", "settings.system-settings.input.footer.label": "Fusszeile", "settings.system-settings.input.footer.placeholder.label": "Fußzeile eingeben", "settings.payment-gateway.title": "Zahlungs-Gateways", "settings.payment-gateway.input.stripe-key.label": "STREIFENSCHLÜSSEL", "settings.payment-gateway.input.stripe-key.placeholder.label": "<PERSON><PERSON> lassen Sie dieses <PERSON><PERSON> leer, wenn Sie es nicht geändert haben", "settings.payment-gateway.input.stripe-secret.label": "STREIFEN GEHEIM", "settings.payment-gateway.switch-btn.label": "Stripe-API-Schlüssel löschen", "settings.sms-configuration.title": "SMS-Konfiguration", "settings.sms-configuration.select.sms-gateway.label": "SMS-Gateway", "settings.sms-configuration.select.sms-gateway.placeholder.label": "Wählen Sie SMS-Gateway", "settings.sms-configuration.input.twilio-sid.label": "TWILIO-SID", "settings.sms-configuration.input.twilio-token.label": "TWILIO-TOKEN", "settings.sms-configuration.select.twilio-from.label": "TWILIO VON", "settings.sms-configuration.select.twilio-from.placeholder.label": "Geben Sie TWILIO VON ein", "settings.sms-configuration.input.twilio-sid.placeholder.label": "<PERSON><PERSON><PERSON> Sie die TWILIO-SID ein", "settings.smtp-configuration.title": "SMTP-Konfiguration", "settings.smtp-configuration.input.host.label": "GASTGEBER", "settings.smtp-configuration.input.port.label": "HAFEN", "settings.smtp-configuration.input.username.label": "<PERSON><PERSON><PERSON><PERSON>", "settings.smtp-configuration.input.password.label": "Passwort", "settings.smtp-configuration.input.encryption.label": "Verschlüsselung", "settings.clear-cache.title": "<PERSON><PERSON> le<PERSON>n", "settings.system-settings.select.default-currency.validate.label": "Bitte Währung auswählen", "settings.system-settings.input.company-name.validate.label": "<PERSON>te geben Si<PERSON> den Firmennamen ein", "settings.system-settings.input.company-phone.validate.label": "Bitte Firmentelefon eingeben", "settings.system-settings.input.developed-by.validate.label": "<PERSON>te geben Si<PERSON> entwickelt von ein", "settings.system-settings.input.footer.validate.label": "Bitte Stadt eingeben", "settings.system-settings.select.default-language.validate.label": "Bitte Sprache auswählen", "settings.system-settings.select.default-customer.validate.label": "Bitte Kunden auswählen", "settings.system-settings.select.default-warehouse.validate.label": "Bitte Lager auswählen", "settings.system-settings.select.address.validate.label": "<PERSON><PERSON> Adresse e<PERSON>ben", "settings.system-settings.select.address.valid.validate.label": "Die Adresse darf nicht länger als 150 Zeichen sein", "settings.sms-configuration.select.sms-gateway.validate.label": "Bitte SMS-Gateway auswählen", "settings.sms-configuration.input.twilio-sid.validate.label": "<PERSON>te geben Sie sid ein", "settings.sms-configuration.input.twilio-token.validate.label": "Bitte Token e<PERSON>ben", "settings.sms-configuration.select.twilio-from.validate.label": "<PERSON>te geben <PERSON> twillo from ein", "settings.smtp-configuration.input.host.validate.label": "Bitte SMTP-Host eingeben", "settings.smtp-configuration.input.port.validate.label": "Bitte SMTP-Port eingeben", "settings.smtp-configuration.input.username.validate.label": "Bitte geben sie einen Benutzernamen ein", "settings.smtp-configuration.input.password.validate.label": "Bitte Passwort eingeben", "settings.smtp-configuration.input.encryption.validate.label": "Bitte geben Sie die Verschlüsselung ein", "settings.success.edit.message": "Einstellung erfolgreich aktualisiert", "update-profile.input.full-name.label": "Vollständiger Name", "update-profile.title": "Profildetails", "update-profile.tab.title": "Profil aktualisieren", "update-profile.success.update.message": "<PERSON>il er<PERSON><PERSON>g<PERSON>ich aktualisiert", "change-password.input.current.label": "Aktuelles Passwort", "change-password.input.new.label": "Neues Kennwort", "change-password.input.confirm.label": "Bestätige das Passwort", "change-password.input.current.placeholder.label": "Aktuelles Passwort eingeben", "change-password.input.new.placeholder.label": "Neues Passwort eingeben", "change-password.input.confirm.placeholder.label": "Geben Sie das Kennwort bestätigen ein", "change-password.input.current.validate.label": "Aktuelles Passwort eingeben", "change-password.input.new.validate.label": "Neues Passwort eingeben", "change-password.input.confirm.validate.label": "Passwort bestätigen eingeben", "change-password.input.confirm.valid.validate.label": "Die Passwortbestätigung stimmt nicht überein", "login-form.title": "Anmelden", "login-form.login-btn.label": "<PERSON><PERSON><PERSON><PERSON>", "login-form.forgot-password.label": "Passwort vergessen ?", "forgot-password-form.reset-link-btn.label": "Link zum Zurücksetzen des Passworts senden", "forgot-password-form.success.reset-link.label": "Wir haben Ihren Link zum Zurücksetzen des Passworts per E-Mail gesendet!", "login.success.message": "Erfolgreich eingeloggt.", "logout.success.message": "Abmeldung erfolgreich.", "change-language.update.success.message": "Sprache erfolgreich aktualisiert", "reset-password.title": "Passwort zurücksetzen", "reset-password.password.validate.label": "Das Bestätigungspasswort und das Passwort müssen übereinstimmen", "reset-password.success.update.message": "Dein Passwort wurde zurück gesetzt!", "sales.title": "Umsätze", "sale.title": "<PERSON><PERSON><PERSON><PERSON>", "sale.create.title": "Verkauf erstellen", "sale.edit.title": "Verkauf bearbeiten", "sale.select.customer.label": "Kunde", "sale.select.customer.placeholder.label": "Wählen Sie Kunde", "sale.select.customer.validate.label": "Bitte Kunden auswählen", "sale.select.payment-status.placeholder": "Wählen Sie Zahlungsstatus", "sale.order-item.table.net-unit-price.column.label": "Netto-Einheitspreis", "sale.product.table.no-data.label": "<PERSON><PERSON> ve<PERSON>ü<PERSON>", "sale.success.create.message": "Verkauf erfolgreich erstellt", "sale.success.edit.message": "Verkauf erfolgreich aktualisiert", "sale.success.delete.message": "Verkauf erfolgreich <PERSON>t", "sale.details.title": "Verkaufsdetail", "sale.detail.customer.info": "Kundeninformation", "sale.detail.invoice.info": "<PERSON><PERSON><PERSON>ng<PERSON><PERSON>", "sales-return.title": "Verkaufserlöse", "sale-return.title": "Verkauf zurück", "sale-return.create.title": "Verkaufsrückgabe erstellen", "sale-return.edit.title": "Verkaufsretoure bearbeiten", "sale-return.success.create.message": "Verkaufsrückgabe erfolgreich erstellt", "sale-return.success.edit.message": "Verkauf Rückgabe erfolgreich aktualisiert", "sale-return.success.delete.message": "Sale Retoure erfolgreich <PERSON>t", "sale-return.details.title": "Verkauf Rücksendedetails", "date-picker.filter.today.label": "<PERSON><PERSON>", "date-picker.filter.this-week.label": "In dieser <PERSON><PERSON>e", "date-picker.filter.last-week.label": "Letzte Woche", "date-picker.filter.this-month.label": "<PERSON><PERSON>", "date-picker.filter.last-month.label": "Letzten Monat", "date-picker.filter.Custom-Range.label": "Benutzerdefinierten Bereich", "date-picker.filter.reset.label": "Z<PERSON>ücksetzen", "date-picker.filter.apply.label": "<PERSON><PERSON><PERSON>", "date-picker.filter.placeholder.label": "Da<PERSON> ausw<PERSON>en", "bar.title": "Bar", "line.title": "<PERSON><PERSON>", "filter.label": "Filter", "reports.title": "Bericht", "warehouse.reports.title": "Lagerbericht", "sale.reports.title": "Verkaufsbericht", "stock.reports.title": "Bestandsbericht", "purchase.reports.title": "Kaufbericht", "top-selling-product.reports.title": "Bericht über die meistverkauften Produkte", "globally.react-table.column.code.label": "Code", "print.barcode.title": "Barcode drucken", "paper.size.title": "<PERSON><PERSON><PERSON> g<PERSON>", "paper.size.placeholder.label": "Wählen Sie Papiergröße", "globally.paper.size.validate.label": "Bitte wählen Sie das Papierformat aus", "print.validate.label": "Bitte aktualisieren Sie den Barcode zum Drucken", "current.stock.label": "Aktueller Lagerbestand", "stock.report.details.title": "Bestandsberichtsdetails", "print.title": "<PERSON><PERSON><PERSON>", "update.title": "Aktualisieren", "preview.title": "Vorschau", "toast.successful.title": "Erfolgreich", "toast.error.title": "Etwas ist schief gelaufen!", "unit.filter.all.label": "Alle", "unit.filter.piece.label": "Stück", "unit.filter.meter.label": "<PERSON>er", "unit.filter.kilogram.label": "Kilogramm", "status.filter.received.label": "<PERSON><PERSON><PERSON><PERSON>", "status.filter.pending.label": "<PERSON><PERSON><PERSON><PERSON>", "status.filter.ordered.label": "<PERSON><PERSON><PERSON>", "payment-status.filter.paid.label": "Be<PERSON>hlt", "payment-status.filter.unpaid.label": "Unbezahlt", "excel.btn.label": "AUSGEZEICHNET", "pdf.btn.label": "PDF", "cash.label": "<PERSON><PERSON>", "no-option.label": "<PERSON><PERSON>en", "warehouse.details.title": "Lagerdetails", "select.payment-type.label": "Zahlungsart", "payment-type.filter.cheque.label": "Überprüfen", "payment-type.filter.bank-transfer.label": "Banküberweisung", "payment-type.filter.other.label": "Sonstiges", "paying-amount-title": "Zahlungsbetrag", "create-payment-title": "Zahlung erstellen", "reference-placeholder-label": "Referenz eingeben", "input-Amount-to-pay-title": "<PERSON><PERSON>", "edit-payment-title": "Zahlung bearbeiten", "no-product-found.label": "Kein Produkt gefunden", "sale.select.payment-type.placeholder": "Wählen Sie Zahlungsart", "globally.payment.type.validate.label": "Bitte Zahlungsart auswählen", "product.quantity.alert.reports.title": "Produktmengenwarnungen", "globally-saving-btn-label": "Sparen...", "payment-status.filter.partial.label": "Teilweise", "dashboard.recentSales.total-product.label": "Produkte insgesamt", "adjustments.title": "Anpassungen", "adjustments.create.title": "Anpassung erstellen", "adjustments.edit.title": "Anpassung bearbeiten", "adjustments.detail.title": "Anpassungsdetails", "Adjustment.success.create.message": "Anpassung erfolgreich erstellt", "Adjustment.success.edit.message": "Anpassung erfolgreich aktualisiert", "Adjustment.success.delete.message": "Anpassung erfolgreich gelöscht", "login-form.go-to-sign-in.label": "Zurück zum Anmelden", "pos-product.title": "PRODUKT", "pos-qty.title": "<PERSON><PERSON>", "pos-price.title": "PREIS", "pos-sub-total.title": "ZWISCHENSUMME", "pos-total-qty.title": "Gesamtmenge", "pos-total.title": "Gesamt", "pos-pay-now.btn": "Zahlen Sie jetzt", "pos-globally.search.field.label": "Produkt nach Codenamen scannen/suchen", "pos-make-Payment.title": "Zahlung leisten", "pos-received-amount.title": "Erhaltener Betrag", "pos-total-amount.title": "Gesamtsumme", "pos-sale.detail.invoice.info": "<PERSON><PERSON><PERSON><PERSON>", "pos-sale.detail.Phone.info": "Telefon", "pos-sale.detail.Paid-bt.title": "<PERSON><PERSON><PERSON><PERSON> von", "pos-close-btn.title": "Nah dran", "pos-item.print.invoice.title": "Artikel", "pos-all.categories.label": "Alle Kategorien", "pos-all.brands.label": "<PERSON>e Marken", "pos-no-product-available.label": "<PERSON>ine Produkte verfügbar", "pos-sale.select.discount-type.placeholder": "Wählen Sie Rabattart", "pos-sale.select.sale-unit-type.placeholder": "Wählen Sie den Typ der Verkaufseinheit", "pos-thank.you-slip.invoice": "Danke für ihren Einkauf. Bitte besuchen Sie uns erneut", "pos.payment.success.message": "Zahlung erfolgreich durchgeführt", "pos.cash-payment.product-error.message": "Bitte Produkt in den Warenkorb legen", "pos.cash-payment.quantity-error.message": "Bitte Produktmenge hinzufügen", "pos.cash-payment.tax-error.message": "Bitte geben Sie einen Steuerwert zwischen 0 und 100 ein", "pos.cash-payment.total-amount-error.message": "Der Rabattbetrag sollte nicht größer als der Gesamtbetrag sein", "pos.subtotal.small.title": "Zwischensumme", "settings.system-settings.select.default-version-footer.placeholder.label": "Versionsnummer in Fußzeile anzeigen", "settings.system-settings.select.logo.placeholder.label": "Logo im Einzahlungsschein anzeigen", "settings.system-settings.select.appname-sidebar.placeholder.label": "App-Namen in der Seitenleiste anzeigen", "pos.cash-payment.sub-total-amount-error.message": "Der Versandbetrag sollte nicht größer als die Zwischensumme sein", "product.import.title": "Produkte importieren", "globally.sample.download.label": "Be<PERSON>piel herunterladen", "product-code.import.required-highlight.message": "Code darf nicht bereits vorhanden sein", "product-unit.import.required-highlight.message": "Einheit muss bereits erstellt sein. Bitte verwenden Sie den vollständigen Namen der Einheit", "globally.optional-input.validate.label": "Feld optional", "reset.title": "Z<PERSON>ücksetzen", "reset.yes.title": "<PERSON><PERSON>, zurücksetzen", "dashboard.widget.today-total-purchases.label": "Heute Gesamteinkäufe", "dashboard.widget.today-payment-received.label": "Heute insgesamt erhalten (Verkäufe)", "dashboard.widget.today-total-sales.label": "Heute Gesamtumsatz", "dashboard.widget.today-total-expense.label": "Heute Gesamtausgaben", "reset.modal.msg": "Möchten Si<PERSON> wirklich zurücksetzen?", "globally.file.validate.label": "Bitte Datei auswählen", "globally.csv-file.validate.label": "Bitte csv-Date<PERSON> auswählen", "file.success.upload.message": "Datei erfolgreich hoch<PERSON>aden", "transfers.title": "Überweisungen", "transfer.title": "Transfer", "transfer.create.title": "Überweisung erstellen", "transfer.edit.title": "Übertragung bearbeiten", "transfer.from-warehouse.title": "<PERSON><PERSON>", "transfer.to-warehouse.title": "<PERSON>um Lager", "transfer.success.create.message": "Übertragung erfolgreich erstellt", "transfer.success.edit.message": "Übertragung erfolgreich aktualisiert", "transfer.success.delete.message": "Übertragung erfolgreich gelöscht", "status.filter.complated.label": "Zusammengesetzt", "status.filter.sent.label": "Gesendet", "settings.prefixes-settings.input.purchases.placeholder.label": "PU", "settings.prefixes-settings.input.purchases-return.placeholder.label": "PR", "settings.prefixes-settings.input.sales.placeholder.label": "SL", "settings.prefixes-settings.input.salse-return.placeholder.label": "SR", "settings.prefixes-settings.input.expense.placeholder.label": "EX", "settings.prefixes-settings.input.purchases.validate.label": "<PERSON>te geben Sie Käufe ein", "settings.prefixes-settings.input.purchases-return.validate.label": "Bitte geben Sie die Einkäufe zurück", "settings.prefixes-settings.input.sales.validate.label": "Bitte Umsatz eingeben", "settings.prefixes-settings.input.salse-return.validate.label": "<PERSON>te geben Sie die Retoure ein", "settings.prefixes-settings.input.expense.validate.label": "Bitte geben Sie die Ausgaben ein", "side-menu.empty.message": "<PERSON>ine übereinstimmenden Aufzeichnungen gefunden", "product.export.title": "Produkte exportieren", "globally.input.content.label": "Inhalt", "email-template.edit.title": "E-Mail-Vorlage bearbeiten", "email-template.title": "E-Mail-Vorlagen", "email-template.success.edit.message": "E-Mail-Vorlagen erfolgreich aktualisiert.", "transfer.details.title": "Übertragungsdetails", "setting.state.lable": "Bundesland", "setting.postCode.lable": "<PERSON><PERSON><PERSON><PERSON>", "settings.system-settings.select.state.validate.label": "Bitte Bundesland auswählen", "settings.system-settings.select.country.validate.label": "Bitte Land auswählen", "purchases.total.amount.title": "Gesamtbetrag der Käufe", "purchases-return.total.amount.title": "Einkäufe geben den Gesamtbetrag zurück", "supplier.report.details.title": "Lieferantenberichtsdetails", "supplier.report.title": "Lieferantenbericht", "prefix.title": "Präfixe", "quotations.title": "Zitate", "create-quotation.title": "Ang<PERSON><PERSON> erstellen", "edit-quotation.title": "Zitat bearbeiten", "details-quotations.title": "Angebotsdetails", "quotation.success.create.message": "Angebot erfolgreich erstellt", "quotation.success.edit.message": "Angebot erfolgreich aktualisiert", "quotation.success.delete.message": "Angebot erfolgreich <PERSON>", "settings.system-settings.select.date-format.label": "Datei Format", "quotation.title": "Zitat", "quotation.detail.invoice.info": "Angebotsinfo", "converted.status.label": "Umgewandelt", "settings.system-settings.select.postcode.validate.length.label": "Die Postleitzahl sollte nicht länger als 8 sein", "mail-settings.sender-name.title": "Absender", "mail-settings.title": "E-Mail-Einstellungen", "mail-settings.success.edit.message": "E-Mail-Einstellungen erfolgreich aktualisiert", "best-customer.report.title": "<PERSON><PERSON>", "sms-template.edit.title": "SMS-Vorlage bearbeiten", "sms-template.title": "SMS-Vorlagen", "sms-template.success.edit.message": "SMS-Vorlage erfolgreich aktualisiert.", "sms-content-variables.title": "SMS-INHALTSVARIABLEN", "email-content-variables.title": "E-MAIL-INHALTSVARIABLEN", "sms-content-text.error.message": "Sie haben die maximal zulässige Zeichenzahl 160 erreicht.", "sms-content.error.message": "Inhalt muss verlangt werden", "profit-loss.reports.title": "Profiteinbuße", "global.revenue.title": "Einnahmen", "global.gross-profit.title": "B<PERSON><PERSON><PERSON><PERSON><PERSON>", "global.payment-received.title": "Eingegangene Zahlungen", "global.payment-sent.title": "Bezahlung gesendet", "global.net-payment.title": "Zahlungen netto", "customer.report.details.title": "Details zum Kundenbericht", "customer.report.title": "Kundenberichte", "sale.payment.report.title": "Verkaufszahlung", "sale.total.amount.title": "Verkaufsgesamtbetrag", "sale-return.total.amount.title": "Gesamtbetrag der Verkaufsrückgabe", "sale-Due.total.amount.title": "Geschuldeter Gesamtbetrag", "sale-paid.total.amount.title": "Bezahlter Gesamtbetrag", "sale-reference.title": "Verkaufsreferenz", "pepole.title": "<PERSON><PERSON><PERSON><PERSON>", "more-report.option.title": "<PERSON><PERSON>", "currency.icon.right.side.lable": "Währungssymbol Rechte Seite", "settings.system-settings.select.postcode.validate.label": "<PERSON><PERSON> Postleitzahl auswählen", "pos.register.payment-method.": "Zahlungsmethode", "pos.register-details.sell.title": "verkaufen", "total.sales.title": "Gesamtumsatz", "email.status.edit.success.message": "E-Mail-Status erfolgreich aktualisiert.", "sms.status.edit.success.message": "SMS-Status erfolgreich aktualisiert.", "sms-api.title": "SMS-API", "key.lable": "Taste", "key.value.lable": "Schlüsselwert", "url.lable": "URL", "mobile.key.lable": "<PERSON><PERSON>", "message.key.lable": "Nachrichtenschlüssel", "sms.status.lable": "SMS-Status", "active.status.lable": "Aktiv", "in-active.status.lable": "Inaktiv", "sms.api.update.success.message": "SMS-API-Update Erfolgreich.", "sale-return.product-qty.validate.message": "Die zurückgegebene Menge ist größer als die verkaufte Menge", "template.title": "Vorlagen", "pos.product-quantity-error.message": "<PERSON><PERSON> Menge mehr verfügbar.", "product-list.lable": "Produktliste", "register.details.title": "Registerdetails", "register.total-sales.label": "Gesamtumsatz", "register.total-refund.title": "Gesamtrückerstattung", "register.total-payment.title": "Gesamtzahlung", "register.product.sold.title": "Details der verkauften Produkte", "register.product.sold.by.brand.title": "Details der verkauften Produkte (nach Marke)", "print-barcode.show-company.label": "Store-Name anzeigen", "print-barcode.show-product-name.label": "Produktname anzeigen", "print-barcode.show-price.label": "<PERSON><PERSON> anzeigen", "product.input.quantity-limitation.label": "Mengenbegrenzung", "product.input.quantity-limitation.placeholder": "Mengenbegrenzung eingeben", "pos.hold-list-btn.title": "Halt", "create.hold-list.warning.message": "Rechnung aufbewahren? Dieselbe Referenz ersetzt die alte Liste, falls vorhanden!!", "create-modal.yes.ok-btn": "Ja ok", "hold-list.reference-number.placeholder": "Bitte Referenznummer eingeben!", "hold-list-id.table.column.label": "ID", "hold-list-ref-id.table.column.label": "Ref.ID", "hold-list.details.title": "Wartelist<PERSON>", "hold-list.success.create.message": "Hold-Liste erfolgreich erstellt", "hold-list.success.delete.message": "Hold-Liste erfolgreich gel<PERSON>t", "report-all.warehouse.label": "Alle Lager", "setting.mail-mailer.lable": "MAIL-VERSANDER", "setting.mail-host.lable": "MAIL-HOST", "setting.mail-port.lable": "POSTHAFEN", "setting.mail-user-name.lable": "MAIL-BENUTZERNAME", "setting.mail-password.lable": "MAIL-PASSWORT", "setting.mail-encryption.lable": "MAIL-VERSCHLÜSSELUNG", "sale.payment.create.success": "Verkaufszahlung erfolgreich erstellt", "sale.payment.edit.success": "Verkaufszahlung erfolgreich aktualisiert", "paying-amount-validate-label": "Der Zahlungsbetrag sollte kleiner oder gleich dem zu zahlenden Betrag sein", "hold-list.reference-code.error": "Das Feld Referenzcode ist erforderlich.", "settings.clear-cache.success.message": "<PERSON><PERSON> er<PERSON><PERSON><PERSON><PERSON><PERSON>", "product.product-in-stock.label": "<PERSON><PERSON>", "product-items.label": "Artikel", "Payload.key.lable": "Nutzlast", "base-units.title": "Basiseinheiten", "base-unit.create.title": "Basiseinheit erstellen", "base-unit.edit.title": "Basiseinheit bearbeiten", "base-unit.title": "Grundeinheit", "base-unit.success.create.message": "Basiseinheit erfolgreich erstellt", "base-unit.success.edit.message": "Base Unit erfolgreich aktualisiert", "base-unit.success.delete.message": "Base Unit erfolgreich gelöscht", "DOB.input.label": "Geburtsdatum", "purchase.product.quantity.validate.label": "Bitte Produktmenge eingeben", "languages.title": "<PERSON><PERSON><PERSON>", "react-data-table.translation.column.label": "Übersetzung", "react-data-table.iso-date.column.label": "ISO-Code", "globally.input.iso-code.validate.label": "Bitte ISO-Code eingeben", "globally.input.iso-code.character.validate.label": "Die Länge des ISO-Codes sollte gleich 2 sein", "translation.manager.title": "Übersetzungsmanager", "language.updated.success.message": "Sprache erfolgreich aktualisiert", "language.deleted.success.message": "Sprache erfolgreich <PERSON>t", "language.edit.success.message": "Sprache erfolgreich bearbeitet", "language.save.success.message": "Sprache erfolgreich erstellt", "language.enabled.success.message": "Sprache erfolgreich aktiviert", "language.disabled.success.message": "Sprache erfolgreich deaktiviert", "language.current-language-disable.error.message": "Sie können Ihre aktuell ausgewählte Sprache nicht deaktivieren", "header.profile-menu.change-language.label": "Sprache ändern", "language.title": "<PERSON><PERSON><PERSON>", "pos-paying-amount.title": "Zahlungsbetrag", "pos.change-return.label": "Rückgabe ändern", "language.create.title": "Sprache erstellen", "purchase.less.recieving.ammout.error": "Der empfangene Betrag ist kleiner als die Gesamtsumme.", "add-stock.title": "Stock hinzufügen", "product-quantity.add.title": "Produktmenge hinzufügen", "edit-translation.title": "Übersetzung bearbeiten", "globally.input.cash-in-hand.label": "Bargeld in der Hand", "globally.close-register.title": "Registrierung schließen", "register.closed.successfully.message": "Register erfolgreich geschlossen.", "register.entry.added.successfully.message": "Registereintrag erfolgreich hinzugefügt.", "globally.total-cash.label": "Gesamtbargeld", "globally.input.note.label": "<PERSON><PERSON><PERSON><PERSON>", "globally.input.note.placeholder.label": "<PERSON><PERSON> e<PERSON>ben", "register.report.title": "Bericht registrieren", "user-details.table.opened-on.row.label": "Geöffnet am", "user-details.table.closde-on.row.label": "Geschlossen am", "globally.input.cash-in-hand-while-closing.label": "<PERSON>gel<PERSON> beim <PERSON>", "pos.cclose-register.enter-total-cash.message": "Bitte fügen Sie den gesamten Bargeldbetrag hinzu.", "register.is.still.open.message": "Register ist noch offen!!!", "Are.you.sure.you.want.to.go.to.dashboard.message": "Sind <PERSON> sic<PERSON>, dass Sie zum Dashboard gehen möchten?", "product.quantity.title": "Produktmenge", "pos.this.product.out.of.stock.message": "Dieses Produkt ist nicht vorrätig", "pos.quantity.exceeds.quantity.available.in.stock.message": "Menge überschreitet die im Lager verfügbare Menge", "yes.modal.title": "<PERSON>a", "no.modal.title": "NEIN", "language.edit.title": "Sprache bearbeiten", "select.user.label": "<PERSON><PERSON><PERSON> w<PERSON>hlen", "suppliers.import.title": "Lieferanten importieren", "customers.import.title": "Kunden importieren", "products.type.single-type.label": "Single", "product.type.label": "Produkttyp", "product.type.placeholder.label": "Produkttyp auswählen", "product.type.input.validation.error": "Bitte wählen Sie den Produkttyp aus", "variation.name": "Variationsname", "variation.variation_types": "Variationstypen", "variations.title": "Variationen", "variation.title": "Variation", "variation.create.title": "Variation erstellen", "variation.edit.title": "Variation bearbeiten", "variation.input.name.label": "Name", "variation.input.name.placeholder.label": "Name e<PERSON>ben", "variation.input.name.validate.label": "<PERSON>te geben Si<PERSON> einen Namen ein", "variation.success.create.message": "Variation erfolgreich erstellt", "variation.success.edit.message": "Variation erfolgreich bearbeitet", "variation.success.delete.message": "Variation erfolgreich <PERSON>", "variation.types.title": "Variationstypen", "variation.type.title": "Variationstyp", "variation.type.input.name.placeholder.label": "<PERSON>te geben Si<PERSON> einen Variations<PERSON> ein", "variation.type.input.name.validate.label": "Bitte geben Sie gültige Variationstypen ein", "variation.select.validation.error.message": "Bitte wählen Sie eine Variation aus", "variation.type.select.validate.error.message": "Bitte wählen Sie Variationstypen aus", "products.warehouse.title": "Produktlager", "barcode-symbol-uppercase-validation-message": "Bitte geben Sie für den Code 39-Barcode nur Großbuchstaben und Zahlen ein.", "sale.product-qty.limit.validate.message": "<PERSON>e können nicht mehr als die begrenzte Menge kaufen", "receipt-settings.title": "Receipt-Einstellungen", "receipt-settings.show-warehouse.label": "Lager anzeigen", "receipt-settings.show-email.label": "E-Mail anzeigen", "receipt-settings.show-address.label": "<PERSON><PERSON><PERSON> anzeigen", "receipt-settings.show-customer.label": "Kunde anzeigen", "receipt-settings.show-phone.label": "Telefon anzeigen", "receipt-settings.show-discount-shipping.label": "Rabatt und Versand anzeigen", "receipt-settings.success.edit.message": "Quittungseinstellung erfolgreich aktualisiert", "receipt-settigns.input.note.validate.label": "Bitte Notiz eingeben", "receipt-settings.show-barcode.label": "<PERSON><PERSON><PERSON> thị mã vạch trong biên nhận", "receipt-settings.show-note.label": "<PERSON><PERSON> anzeigen", "globally.submit-and-print-button": "Senden und Drucken", "receipt-settings.show-product-code.label": "Produktcode anzeigen", "globally.footer.label": "Alle Rechte vorbehalten", "logout.confirmation.label": "Möchten Sie sich wirklich abmelden?", "addition.title": "Addition", "subtraction.title": "Subtraktion", "print-custom-barcode.title": "Benutzerdefinierten Barcode drucken", "product.sku.label": "SKU/Barcode", "add.stock.while.product.creation.title": "Lagerbestand während der Produkterstellung hinzufügen", "confirm-modal.msg": "Sind Sie sicher?", "store.title": "Filialen", "create.store.title": "Filiale erstellen", "edit.store.title": "Filiale bearbeiten", "store.name.title": "Filialenname", "store.name.placeholder.title": "Filialennamen eingeben", "store.name.validate.label": "Bitte Filialennamen eingeben", "store.success.create.message": "Filiale erfolgreich erstellt", "store.success.edit.message": "Filiale erfolgreich aktualisiert", "store.success.delete.message": "Filiale erfolgreich gel<PERSON>scht", "store.changed.message": "Filiale erfolgreich geändert", "store.header.name.title": "Filiale", "select.all.store.title": "Alle Filialen auswählen", "store.field.must.required.validate": "Filialenfeld muss erforderlich sein", "store.assigned.title": "Filialen zugewiesen", "no.store.title": "<PERSON><PERSON>", "paid.amount.title": "Bezahlter Betrag", "taxes.title": "Steuern", "tax.title": "<PERSON><PERSON><PERSON>", "add.tax.title": "Steuer hinzufügen", "edit.tax.title": "<PERSON>euer bearbeiten", "tax.name.title": "<PERSON><PERSON><PERSON><PERSON>", "tax.name.placeholder.title": "<PERSON><PERSON><PERSON><PERSON>", "tax.name.validate.title": "Bitte Steuername eingeben", "tax.value.title": "Steuerwert", "tax.value.placeholder.title": "Steuerwert eingeben", "tax.value.validate.title": "Bitte Steuerwert eingeben", "tax.deleted.success.message": "Steuer erfolgreich <PERSON>", "tax.edit.success.message": "Steuer erfolgreich bearbeitet", "tax.save.success.message": "Steuer erfolgreich erstellt", "tax.name.unique.validate.title": "Steuername existiert bereits", "tax.value.unique.validate.title": "Steuerwert existiert bereits", "tax.show.on.receipt.pdf.title": "<PERSON><PERSON>/PDF anzeigen", "pos.settings.title": "POS-Einstellungen", "enable.pos.sound.title": "POS-Klick-Sound aktivieren", "show.out.of.stock.product.in.pos": "Nicht vorrätige Produkte im POS anzeigen", "pos.sound.title": "POS-Sound", "upload.audio.title": "Audio hochladen", "pos.audio.required": "Bitte Audio hochladen", "date.of.birth.title": "Geburtsdatum", "customer.details.title": "Kundendaten", "pos.audio.length.tooltip.title": "Die Audiolänge sollte weniger als 3 Sekunden betragen.", "select.date.of.birth": "Wählen Sie Ihr Geburtsdatum", "item.deleted.success.message": "Element erfolgreich <PERSON>t", "payment.method.save.success.message": "Zahlungsmethode erfolgreich erstellt", "payment.method.edit.success.message": "Zahlungsmethode erfolgreich bearbeitet", "payment.method.deleted.success.message": "Zahlungsmethode erfolgreich gelöscht", "payment.method.name.unique.validate.title": "Zahlungsmethode existiert bereits", "show.tax.title": "Steuer anzeigen", "expiry.date.title": "Ablaufdatum", "expiry.date.placeholder.title": "Ablaufdatum e<PERSON>", "payment.method.title": "Zahlungsmethode", "dual.screen.settings.title": "Dual-Screen-Einstellungen", "dual.screen.display.header.title": "Anzeigekopfzeile", "dual.screen.display.header.placeholder.title": "Anzeigekopfzeile eingeben", "please.enter.display.header.title": "Bitte Anzeigekopfzeile eingeben", "carousel.image.title": "Karussellbilder", "validation.you.can.upload.maximum.images": "Sie können maximal 5 Bilder hochladen.", "upload.maximum.images": "Laden Sie maximal fünf Bilder hoch.", "no.customer.selected.title": "Kein Kunde ausgewählt", "send.test.email.title": "Test-E-Mail senden", "send.test.email.success.message": "Test-E-Mail erfolgreich gesendet.", "globally.receipt.download.label": "<PERSON><PERSON><PERSON><PERSON> herunt<PERSON><PERSON>n", "load.more.title": "<PERSON><PERSON> <PERSON>", "pos.edit.sale.title": "Möchten Sie diesen Verkauf wirklich bearbeiten?", "sale.payment.total-exceed.validate.message": "Der Gesamtzahlungsbetrag darf den Gesamtbetrag nicht überschreiten.", "pos.payment.amount.exceeds.total.error": "Der Zahlungsbetrag darf den Gesamtbetrag nicht überschreiten", "pos.payment.total.exceeds.grand.total.error": "Der Gesamtbetrag überschreitet den Gesamtbetrag", "globally.payment.details.validate.label": "Bitte überprüfen Sie die Zahlungsdetails auf Fehler", "globally.detail.payment.details": "Zahlungsdetails", "globally.input.width.label": "Breite", "globally.input.height.label": "<PERSON><PERSON><PERSON>", "globally.input.width.validate.label": "Bitte Breite eingeben", "globally.input.height.validate.label": "Bitte Höhe e<PERSON>ben"}