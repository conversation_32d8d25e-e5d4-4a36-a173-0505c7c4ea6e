{"dashboard.title": "bảng đi<PERSON><PERSON> k<PERSON>n", "header.pos.title": "POS", "header.profile-menu.profile.label": "<PERSON><PERSON> sơ", "header.profile-menu.change-password.label": "<PERSON><PERSON><PERSON> mật kh<PERSON>u", "header.profile-menu.logout.label": "<PERSON><PERSON><PERSON> xu<PERSON>", "product.categories.title": "<PERSON><PERSON> m<PERSON><PERSON> sản ph<PERSON>m", "expense.categories.title": "<PERSON><PERSON><PERSON> mục <PERSON> phí", "dashboard.salesReturn.title": "<PERSON><PERSON><PERSON> b<PERSON> hàng", "dashboard.top-customers.title": "5 khách hàng hàng đầu", "dashboard.purchaseReturn.title": "<PERSON><PERSON> hàng tr<PERSON> lại", "dashboard.ThisWeekSales&Purchases.title": "<PERSON><PERSON> hàng và mua hàng trong tuần này", "dashboard.TopSellingProducts.title": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON> b<PERSON> ch<PERSON> nh<PERSON>t", "dashboard.stockAlert.title": "<PERSON><PERSON><PERSON> b<PERSON>o hàng tồn kho", "dashboard.recentSales.title": "<PERSON><PERSON> hàng gần đây", "dashboard.PaymentSentReceived.title": "<PERSON><PERSON> toán đã <PERSON> và <PERSON>n", "dashboard.stockAlert.code.label": "Mã số", "dashboard.stockAlert.product.label": "<PERSON><PERSON><PERSON> p<PERSON>m", "dashboard.stockAlert.warehouse.label": "<PERSON><PERSON>", "dashboard.stockAlert.quantity.label": "Số lượng", "dashboard.stockAlert.alertQuantity.label": "Số lư<PERSON> cảnh báo", "dashboard.grantTotal.label": "<PERSON><PERSON><PERSON> cộng", "dashboard.recentSales.reference.label": "TÀI LIỆU THAM KHẢO", "dashboard.recentSales.customer.label": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>ng", "dashboard.recentSales.status.label": "<PERSON><PERSON><PERSON><PERSON> thái", "dashboard.recentSales.paid.label": "Trả", "dashboard.recentSales.due.label": "<PERSON><PERSON><PERSON> h<PERSON>n", "dashboard.recentSales.paymentStatus.label": "<PERSON><PERSON>nh trạng thanh toán", "dataTable.searchBar.placeholder.label": "<PERSON><PERSON><PERSON>", "settings.select.language.label": "<PERSON><PERSON><PERSON>", "settings.select.language.placeholder": "<PERSON><PERSON><PERSON> ngôn ngữ", "users.title": "<PERSON><PERSON><PERSON><PERSON> dùng", "user.create.title": "<PERSON><PERSON><PERSON> dùng", "user.edit.title": "<PERSON><PERSON><PERSON><PERSON> dùng biên tập", "user.input.first-name.label": "Họ", "user.input.last-name.label": "tên họ", "user.input.email.label": "E-mail", "user.input.phone-number.label": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "user.input.password.label": "<PERSON><PERSON><PERSON>", "user.input.confirm-password.label": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u", "user.input.role.label": "<PERSON><PERSON>", "user.input.first-name.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> tên đầu tiên", "user.input.last-name.placeholder.label": "<PERSON><PERSON><PERSON><PERSON>", "user.input.email.placeholder.label": "<PERSON><PERSON><PERSON><PERSON>", "user.input.phone-number.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "user.input.password.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u", "user.input.confirm-password.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>c nhận mật kh<PERSON>u", "user.input.role.placeholder.label": "<PERSON><PERSON><PERSON> vai trò", "users.table.user.column.title": "<PERSON>ư<PERSON>i sử dụng", "users.table.phone-number.column.title": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "users.table.role.column.title": "<PERSON><PERSON>", "users.table.date.column.title": "<PERSON><PERSON><PERSON> t<PERSON>o ra", "user-details.title": "<PERSON> tiết người dùng", "user-details.table.created-on.row.label": "<PERSON><PERSON><PERSON><PERSON> tạo ra", "user-details.table.title": "<PERSON><PERSON><PERSON> quan", "user.input.first-name.validate.label": "<PERSON><PERSON> lòng nhập tên", "user.input.last-name.validate.label": "<PERSON><PERSON> lòng nh<PERSON>p họ", "user.input.email.validate.label": "<PERSON><PERSON> lòng nhập địa chỉ email", "user.input.email.valid.validate.label": "<PERSON><PERSON> lòng nhập địa chỉ email hợp lệ", "user.input.phone-number.validate.label": "<PERSON><PERSON> lòng nhập số điện thoại", "user.input.password.validate.label": "<PERSON>n vui lòng nhập mật kh<PERSON>u", "user.input.confirm-password.validate.label": "<PERSON><PERSON> lòng nhập mật khẩu xác nhận", "user.input.role.validate.label": "<PERSON>ui lòng chọn vai trò", "user.success.create.message": "<PERSON>ư<PERSON><PERSON> dùng đã đư<PERSON><PERSON> tạo thành công", "user.success.edit.message": "<PERSON><PERSON> cập nhật người dùng thành công", "user.success.delete.message": "<PERSON><PERSON> xóa người dùng thành công", "react-data-table.searchbar.placeholder": "<PERSON><PERSON><PERSON>", "react-data-table.action.column.label": "<PERSON><PERSON><PERSON> đ<PERSON>", "react-data-table.date.column.label": "<PERSON><PERSON><PERSON>", "react-data-table.no-record-found.label": "<PERSON><PERSON><PERSON><PERSON> có bản ghi nào để hiển thị", "react-data-table.records-per-page.label": "<PERSON><PERSON><PERSON> li<PERSON>u từng trang", "delete-modal.title": "Xóa bỏ!", "delete-modal.msg": "Bạn có chắc chắn muốn xóa cái này không", "delete-modal.yes-btn": "Có, Xóa!", "delete-modal.no-btn": "Không, Hủy bỏ", "suppliers.title": "<PERSON><PERSON><PERSON> nh<PERSON> cung cấp", "supplier.title": "<PERSON><PERSON><PERSON> cung cấp", "supplier.create.title": "T<PERSON><PERSON> nhà cung cấp", "supplier.edit.title": "Chỉnh sửa nhà cung cấp", "supplier.table.name.column.title": "<PERSON><PERSON><PERSON>", "supplier.table.phone-number.column.title": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "supplier.table.email.column.title": "E-mail", "supplier.table.address.column.title": "Địa chỉ nhà", "supplier.success.create.message": "<PERSON><PERSON><PERSON> cung cấp đã đư<PERSON><PERSON> tạo thành công", "supplier.success.edit.message": "<PERSON><PERSON> cập nhật nhà cung cấp thành công", "supplier.success.delete.message": "Đ<PERSON> xóa nhà cung cấp thành công", "globally.input.name.label": "<PERSON><PERSON><PERSON>", "globally.react-table.column.created-date.label": "<PERSON><PERSON><PERSON><PERSON> tạo ra", "globally.react-table.column.payment-type.label": "<PERSON><PERSON><PERSON> thức thanh toán", "globally.input.name.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> tên", "globally.input.email.label": "E-mail", "globally.input.email.placeholder.label": "<PERSON><PERSON><PERSON><PERSON>", "globally.input.phone-number.label": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "globally.input.phone-number.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "globally.input.country.label": "Quốc gia", "globally.input.country.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> quốc gia", "globally.input.city.label": "<PERSON><PERSON><PERSON><PERSON> phố", "globally.input.city.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> vào thành phố", "globally.input.address.label": "Địa chỉ nhà", "globally.input.address.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "globally.input.notes.label": "<PERSON><PERSON><PERSON>", "globally.input.notes.placeholder.label": "<PERSON><PERSON><PERSON><PERSON>hi <PERSON>ú", "globally.loading.label": "<PERSON><PERSON> lòng chờ...", "globally.input.name.validate.label": "<PERSON><PERSON> lòng nhập tên", "globally.input.email.validate.label": "<PERSON><PERSON> lòng nhập địa chỉ email", "globally.input.email.valid.validate.label": "<PERSON><PERSON> lòng nhập địa chỉ email hợp lệ", "globally.input.country.validate.label": "<PERSON><PERSON> lòng nhập quốc gia", "globally.input.city.validate.label": "<PERSON><PERSON> lòng nhập thành phố", "globally.input.phone-number.validate.label": "<PERSON><PERSON> lòng nhập số điện thoại", "globally.input.address.validate.label": "<PERSON><PERSON> lòng nhập địa chỉ", "globally.input.notes.validate.label": "<PERSON><PERSON><PERSON> ghi chú không được lớn hơn 100 ký tự", "globally.require-input.validate.label": "Trư<PERSON>ng này là bắ<PERSON> buộc", "globally.date.validate.label": "<PERSON><PERSON> lòng nh<PERSON> ng<PERSON>y", "globally.tax-length.validate.label": "<PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> lớn hơn 100", "globally.discount-length.validate.label": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> không đư<PERSON><PERSON> lớn hơn 100", "globally.discount-cost-length.validate.label": "<PERSON><PERSON><PERSON> chiết khấu không đư<PERSON><PERSON> lớn hơn giá thành sản phẩm", "globally.discount-price-length.validate.label": "<PERSON><PERSON><PERSON><PERSON> giá không được lớn hơn giá sản phẩm", "globally.type.label": "<PERSON><PERSON><PERSON> h<PERSON>nh", "globally.back-btn": "Mặt sau", "globally.save-btn": "<PERSON><PERSON><PERSON><PERSON>", "globally.cancel-btn": "Hủy bỏ", "globally.edit-btn": "Chỉnh sửa", "globally.submit-btn": "<PERSON>ộp", "globally.edit.tooltip.label": "Chỉnh sửa", "globally.delete.tooltip.label": "Xóa bỏ", "globally.view.tooltip.label": "<PERSON><PERSON><PERSON><PERSON> xem", "globally.pdf.download.label": "tải PDF", "globally.product-quantity.validate.message": "<PERSON><PERSON> lòng thêm số lượng sản phẩm", "globally.product-already-added.validate.message": "<PERSON><PERSON><PERSON> phẩm này đã đư<PERSON><PERSON> thêm vào", "globally.detail.company.info": "Thông tin công ty", "globally.detail.reference": "<PERSON><PERSON><PERSON> li<PERSON>u tham kh<PERSON>o", "globally.detail.status": "<PERSON><PERSON><PERSON><PERSON> thái", "globally.detail.warehouse": "<PERSON><PERSON>", "globally.detail.payment.status": "<PERSON><PERSON>nh trạng thanh toán", "globally.detail.payment.details": "<PERSON> tiết thanh toán", "globally.detail.order.summary": "<PERSON><PERSON><PERSON> tắt theo thứ tự", "globally.detail.product": "<PERSON><PERSON><PERSON> p<PERSON>m", "globally.detail.net-unit-cost": "Chi phí đơn vị ròng", "globally.detail.net-unit-price": "Đơn gi<PERSON> ròng", "globally.detail.quantity": "Số lượng", "globally.detail.unit-cost": "Đơn giá", "globally.detail.unit-price": "Đơn giá", "globally.detail.discount": "<PERSON><PERSON><PERSON>", "globally.detail.tax": "<PERSON><PERSON><PERSON>", "globally.show.payment.label": "<PERSON><PERSON><PERSON> thị các k<PERSON>n <PERSON>h toán", "globally.detail.subtotal": "Tổng phụ", "globally.detail.order.tax": "<PERSON><PERSON><PERSON> đặt hàng", "globally.detail.shipping": "<PERSON><PERSON> ch<PERSON> hàng", "globally.detail.grand.total": "<PERSON><PERSON><PERSON> cộng", "globally.detail.paid": "Trả", "globally.search.field.label": "<PERSON><PERSON><PERSON> kiếm sản phẩm theo tên mã", "globally.detail.due": "<PERSON><PERSON><PERSON> h<PERSON>n", "globally.status.validate.label": "<PERSON><PERSON> lòng chọn trạng thái", "globally.payment.status.validate.label": "<PERSON>ui lòng chọn trạng thái thanh toán", "customers.title": "<PERSON><PERSON><PERSON><PERSON>", "customer.title": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>ng", "customer.create.title": "<PERSON><PERSON><PERSON> h<PERSON>ng", "customer.edit.title": "Chỉnh sửa kh<PERSON>ch hàng", "customer.success.create.message": "<PERSON><PERSON><PERSON><PERSON> hàng đ<PERSON><PERSON><PERSON> tạo thành công", "customer.success.edit.message": "<PERSON><PERSON> cập nhật khách hàng thành công", "customer.success.delete.message": "<PERSON><PERSON> xóa khách hàng thành công", "warehouse.title": "<PERSON><PERSON>", "warehouse.create.title": "T<PERSON>o nhà kho", "warehouse.edit.title": "Chỉnh sửa kho hàng", "warehouse.details.title": "<PERSON> tiết kho hàng", "warehouse.input.zip-code.label": "<PERSON><PERSON>", "warehouse.input.zip-code.placeholder.label": "Nhập mã zip", "warehouse.input.zip-code.validate.label": "<PERSON><PERSON> lòng nhập mã zip", "warehouse.input.zip-code.valid.validate.label": "<PERSON><PERSON> lòng nhập mã zip hợp lệ", "warehouse.success.create.message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> tạo thành công", "warehouse.success.edit.message": "<PERSON><PERSON> cập nhật kho hàng thành công", "warehouse.success.delete.message": "<PERSON><PERSON> xóa kho thành công", "products.title": "<PERSON><PERSON><PERSON> p<PERSON>m", "product.title": "<PERSON><PERSON><PERSON> p<PERSON>m", "product.create.title": "<PERSON><PERSON><PERSON> s<PERSON>n ph<PERSON>m", "product.edit.title": "Chỉnh s<PERSON>a sản phẩm", "product.input.code.label": "Mã số", "product.input.code.placeholder.label": "<PERSON>h<PERSON><PERSON> mã", "product.input.product-category.label": "danh mục sản phẩm", "product.input.product-category.placeholder.label": "<PERSON><PERSON><PERSON> danh mục sản phẩm", "product.input.brand.label": "<PERSON><PERSON><PERSON><PERSON>", "product.input.brand.placeholder.label": "<PERSON><PERSON><PERSON> th<PERSON> hi<PERSON>u", "product.input.barcode-symbology.label": "<PERSON><PERSON> hi<PERSON>u mã vạch", "product.input.barcode-symbology.placeholder.label": "<PERSON><PERSON><PERSON> ký hiệu mã vạch", "product.input.product-cost.label": "<PERSON><PERSON><PERSON> thành sản phẩm", "product.input.product-cost.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> thành sản phẩm", "product.input.product-price.label": "<PERSON><PERSON><PERSON> s<PERSON>n ph<PERSON>m", "product.input.product-price.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> sản phẩm", "product.input.product-unit.label": "Đơn vị sản phẩm", "product.input.product-unit.placeholder.label": "<PERSON><PERSON><PERSON> đơn vị sản phẩm", "product.input.sale-unit.label": "Đơn v<PERSON> bán hàng", "product.input.sale-unit.placeholder.label": "<PERSON><PERSON><PERSON> đơn vị bán hàng", "product.input.purchase-unit.label": "Đơn vị mua hàng", "product.input.purchase-unit.placeholder.label": "<PERSON><PERSON><PERSON> đơn vị mua hàng", "product.input.stock-alert.label": "<PERSON><PERSON><PERSON> b<PERSON>o hàng tồn kho", "product.input.stock-alert.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> cảnh b<PERSON>o hàng tồn kho", "product.input.order-tax.label": "<PERSON><PERSON><PERSON> đặt hàng", "product.input.order-tax.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> thuế đặt hàng", "product.input.order-tax.validate.label": "<PERSON><PERSON> lòng nhập thuế đặt hàng", "product.input.order-tax.valid.validate.label": "<PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> lớn hơn 100", "product.input.tax-type.label": "<PERSON><PERSON><PERSON> thuế", "product.input.tax-type.placeholder.label": "<PERSON><PERSON><PERSON> lo<PERSON>i thuế", "product.input.warehouse.placeholder.label": "<PERSON><PERSON><PERSON> nh<PERSON> kho", "product.input.multiple-image.label": "<PERSON><PERSON><PERSON><PERSON>", "product.table.image.column.label": "<PERSON><PERSON><PERSON>", "product.table.price.column.label": "<PERSON><PERSON><PERSON> b<PERSON>", "product.product-details.title": "Th<PERSON>ng tin chi tiết sản phẩm", "product.product-details.code-product.label": "<PERSON><PERSON> sản phẩm", "product.product-details.category.label": "<PERSON><PERSON><PERSON>", "product.product-details.cost.label": "<PERSON><PERSON> tổn", "product.product-details.unit.label": "Đơn vị", "product.product-details.tax.label": "<PERSON><PERSON><PERSON>", "product.input.code.validate.label": "vui lòng nhập mã", "product.input.product-category.validate.label": "<PERSON><PERSON> lòng chọn danh mục sản phẩm", "product.input.brand.validate.label": "<PERSON><PERSON> lòng chọn thư<PERSON><PERSON> hiệu", "product.input.barcode-symbology.validate.label": "<PERSON><PERSON> lòng chọn ký hiệu mã vạch", "product.input.product-cost.validate.label": "<PERSON><PERSON> lòng nhập giá thành sản phẩm", "product.input.product-price.validate.label": "<PERSON><PERSON> lòng nhập gi<PERSON> sản phẩm", "product.input.product-unit.validate.label": "vui lòng chọn đơn vị sản phẩm", "product.input.sale-unit.validate.label": "<PERSON><PERSON> lòng chọn đơn vị bán", "product.input.purchase-unit.validate.label": "<PERSON><PERSON> lòng chọn đơn vị mua hàng", "product.input.stock-alert.validate.label": "<PERSON><PERSON> lòng nh<PERSON>p cảnh báo còn hàng", "product.input.tax-type.validate.label": "<PERSON><PERSON> lòng chọn lo<PERSON>i thuế", "product.input.warehouse.validate.label": "<PERSON><PERSON> lòng chọn kho", "product.success.create.message": "<PERSON><PERSON><PERSON> phẩm đ<PERSON><PERSON><PERSON> tạo thành công", "product.success.edit.message": "<PERSON><PERSON> cập nhật sản phẩm thành công", "product.success.delete.message": "<PERSON><PERSON><PERSON> phẩm đã được xóa thành công", "product.image.success.upload.message": "<PERSON><PERSON><PERSON>nh đư<PERSON><PERSON> tải lên thành công", "product.image.success.delete.message": "<PERSON><PERSON> xóa hình ảnh thành công", "brands.title": "<PERSON><PERSON><PERSON><PERSON>", "brand.title": "<PERSON><PERSON><PERSON><PERSON>", "brand.create.title": "<PERSON><PERSON><PERSON> th<PERSON> hi<PERSON>u", "brand.edit.title": "Chỉnh sửa thư<PERSON>ng hiệu", "brand.input.code.label": "Mã số", "globally.input.change-logo.tooltip": "<PERSON>hay đổi biểu trưng", "globally.input.change-image.tooltip": "<PERSON><PERSON><PERSON> h<PERSON>nh <PERSON>", "brand.table.brand-name.column.label": "<PERSON><PERSON><PERSON><PERSON><PERSON> hi<PERSON>", "brand.table.product-count.column.label": "S<PERSON> l<PERSON> sản phẩm", "brand.input.name.valid.validate.label": "<PERSON>ên không đ<PERSON><PERSON><PERSON> lớn hơn 50 ký tự", "brand.success.create.message": "<PERSON><PERSON> tạo thương hiệu thành công", "brand.success.edit.message": "<PERSON><PERSON> cập nhật thư<PERSON>ng hiệu thành công", "brand.success.delete.message": "<PERSON><PERSON> xóa thương hiệu thành công", "product-categories.title": "<PERSON><PERSON> m<PERSON><PERSON> sản ph<PERSON>m", "product-category.title": "danh mục sản phẩm", "product-category.create.title": "<PERSON><PERSON><PERSON> danh mục sản phẩm", "product-category.edit.title": "Chỉnh sửa danh mục sản phẩm", "product-category.success.create.message": "<PERSON><PERSON> mục sản phẩm đã đư<PERSON>c tạo thành công", "product-category.success.edit.message": "<PERSON><PERSON> cập nhật danh mục sản phẩm thành công", "product-category.success.delete.message": "<PERSON><PERSON> x<PERSON>a danh mục sản phẩm thành công", "expense-categories.title": "<PERSON><PERSON><PERSON> mục <PERSON> phí", "expense-category.title": "<PERSON><PERSON><PERSON> mục <PERSON> phí", "expense-category.create.title": "<PERSON><PERSON><PERSON> danh mục chi phí", "expense-category.edit.title": "Chỉnh sửa danh mục chi phí", "expense-category.success.create.message": "<PERSON><PERSON> tạo danh mục chi phí thành công", "expense-category.success.edit.message": "<PERSON><PERSON> cập nhật danh mục chi phí thành công", "expense-category.success.delete.message": "<PERSON><PERSON> xóa danh mục chi phí thành công", "expenses.title": "Chi phí", "expense.title": "Chi phí", "expense.create.title": "Tạo chi phí", "expense.edit.title": "Chỉnh sửa chi phí", "expense.input.details.label": "Th<PERSON>ng tin chi tiết", "expense.input.details.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> chi tiết", "expense.input.amount.label": "Số lượng", "expense.input.title.label": "<PERSON><PERSON><PERSON><PERSON> đề <PERSON> phí", "expense.input.title.validate.label": "<PERSON><PERSON> lòng nhập tiêu đề chi phí", "expense.input.title.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> phí", "expense.input.amount.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> số tiền", "expense.input.warehouse.placeholder.label": "<PERSON><PERSON><PERSON> nh<PERSON> kho", "expense.input.expense-category.placeholder.label": "<PERSON><PERSON><PERSON> loại chi phí", "expense.input.warehouse.validate.label": "<PERSON><PERSON> lòng chọn kho", "expense.input.expense-category.validate.label": "<PERSON><PERSON> lòng chọn danh mục chi phí", "expense.input.amount.validate.label": "<PERSON><PERSON> lòng nh<PERSON>p số tiền", "expense.success.create.message": "<PERSON><PERSON> tạo thành công chi phí", "expense.success.edit.message": "<PERSON><PERSON> cập nhật chi phí thành công", "expense.success.delete.message": "Đã xóa thành công chi phí", "roles.title": "<PERSON>ai trò", "roles.permissions.title": "<PERSON><PERSON> trò / Quyền", "role.title": "<PERSON><PERSON>", "role.create.title": "Tạo vai trò", "role.edit.title": "Chỉnh sửa vai trò", "role.select.all-permission.label": "<PERSON><PERSON><PERSON> cả các quyền", "role.input.permission.label": "<PERSON><PERSON><PERSON><PERSON>", "role.input.name.validate.label": "<PERSON><PERSON> lòng nhập tên", "role.input.name.valid.validate.label": "<PERSON>ên không đ<PERSON><PERSON><PERSON> lớn hơn 50 ký tự", "role.success.create.message": "Đã tạo vai trò thành công", "role.success.edit.message": "<PERSON><PERSON> cập nhật vai trò thành công", "role.success.delete.message": "Đã xóa vai trò thành công", "units.title": "<PERSON><PERSON><PERSON> vị", "unit.title": "Đơn vị", "unit.create.title": "Tạo đơn vị", "unit.edit.title": "Chỉnh sửa đơn vị", "unit.modal.input.short-name.label": "<PERSON><PERSON><PERSON>", "unit.modal.input.short-name.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> tên <PERSON>n", "unit.modal.input.base-unit.label": "Đơn vị cơ sở", "unit.modal.input.base-unit.placeholder.label": "<PERSON><PERSON><PERSON> đơn vị cơ sở", "unit.modal.input.short-name.validate.label": "<PERSON><PERSON> lòng nhập tên ng<PERSON>n", "unit.modal.input.short-name.valid.validate.label": "Tên ngắn không đư<PERSON>c lớn hơn 50 ký tự", "unit.modal.input.base-unit.validate.label": "<PERSON><PERSON> lòng chọn đơn vị cơ sở", "unit.success.create.message": "Đơn vị đư<PERSON>c tạo thành công", "unit.success.edit.message": "<PERSON><PERSON> cập nhật đ<PERSON>n vị thành công", "unit.success.delete.message": "Đơn vị đã đư<PERSON><PERSON> xóa thành công", "currencies.title": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "currency.title": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "currency.create.title": "<PERSON><PERSON><PERSON> ti<PERSON>n tệ", "currency.edit.title": "Chỉnh sửa tiền tệ", "currency.modal.input.name.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> tên tiền tệ", "currency.modal.input.code.label": "Mã số", "currency.modal.input.code.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> mã tiền tệ", "currency.modal.input.symbol.label": "<PERSON><PERSON><PERSON><PERSON>", "currency.modal.input.symbol.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> ký hiệu tiền tệ", "currency.modal.input.name.validate.label": "<PERSON><PERSON> lòng nhập tên đơn vị tiền tệ", "currency.modal.input.code.validate.label": "<PERSON><PERSON> lòng nhập mã đơn vị tiền tệ", "currency.modal.input.code.valid.validate.label": "<PERSON><PERSON> không đư<PERSON><PERSON> lớn hơn 20 ký tự", "currency.modal.input.symbol.validate.label": "<PERSON><PERSON> lòng nhập ký hiệu tiền tệ", "currency.success.create.message": "Đơn vị tiền tệ được tạo thành công", "currency.success.edit.message": "<PERSON><PERSON> cập nhật thành công đơn vị tiền tệ", "currency.success.delete.message": "Đơn vị tiền tệ đã đư<PERSON>c xóa thành công", "purchases.title": "<PERSON><PERSON>", "purchases.purchases-details.title": "<PERSON> tiết mua hàng", "purchase.title": "mua hàng", "purchase.create.title": "<PERSON><PERSON><PERSON> giao d<PERSON>ch mua", "purchase.edit.title": "Chỉnh sửa giao d<PERSON>ch mua", "purchase.select.warehouse.label": "<PERSON><PERSON>", "purchase.select.warehouse.placeholder.label": "<PERSON><PERSON><PERSON> nh<PERSON> kho", "purchase.select.supplier.label": "<PERSON><PERSON><PERSON> cung cấp", "purchase.select.supplier.placeholder.label": "<PERSON><PERSON><PERSON> nhà cung cấp", "purchase.select.warehouse.validate.label": "<PERSON><PERSON> lòng chọn kho", "purchase.select.supplier.validate.label": "<PERSON><PERSON> lòng chọn nhà cung cấp", "purchase.select.status.label": "<PERSON><PERSON><PERSON><PERSON> thái", "purchase.select.status.placeholder.label": "<PERSON><PERSON><PERSON> trạng thái", "purchase.order-item.table.label": "Đặt hàng", "purchase.order-item.table.net-unit-cost.column.label": "Chi phí đơn vị ròng", "purchase.order-item.table.stock.column.label": "<PERSON><PERSON> phần", "purchase.order-item.table.qty.column.label": "Qty", "purchase.order-item.table.discount.column.label": "<PERSON><PERSON><PERSON>", "purchase.order-item.table.tax.column.label": "<PERSON><PERSON><PERSON>", "purchase.order-item.table.sub-total.column.label": "Tổng phụ", "purchase.input.order-tax.label": "<PERSON><PERSON><PERSON> đặt hàng", "purchase.input.shipping.label": "<PERSON><PERSON> ch<PERSON> hàng", "purchase.input.discount.validate.label": "<PERSON><PERSON> lòng nh<PERSON><PERSON> chi<PERSON>t kh<PERSON>u", "purchase.input.order-tax.validate.label": "<PERSON><PERSON> lòng nhập thuế đặt hàng", "purchase.input.shipping.validate.label": "<PERSON><PERSON> lòng nhập giao hàng", "purchase.product-modal.select.discount-type.label": "Loại giảm giá", "purchase.table.column.reference-code.label": "<PERSON><PERSON> tham chiếu", "purchase.grant-total.label": "<PERSON><PERSON><PERSON> cộng", "purchase.product-list.validate.message": "<PERSON><PERSON> lòng thêm sản phẩm vào danh sách", "purchase.success.create.message": "<PERSON><PERSON><PERSON> dịch mua đã đ<PERSON><PERSON><PERSON> tạo thành công", "purchase.success.edit.message": "<PERSON><PERSON> cập nhật giao dịch mua thành công", "purchase.success.delete.message": "<PERSON><PERSON><PERSON> d<PERSON>ch mua đã đ<PERSON><PERSON><PERSON> xóa thành công", "purchase.placeholder.notes.input": "<PERSON><PERSON><PERSON><PERSON>hi <PERSON>ú", "purchases.details.title": "<PERSON> tiết mua hàng", "purchase.detail.supplier.info": "Thông tin nhà cung cấp", "purchase.detail.purchase.info": "Thông tin mua hàng", "purchases.return.title": "<PERSON><PERSON> hàng tr<PERSON> lại", "purchase.return.create.title": "<PERSON><PERSON><PERSON> lợi tức mua hàng", "purchase.return.edit.title": "Chỉnh sửa lợi tức mua hàng", "purchase.return.success.create.message": "<PERSON><PERSON> hàng trả lại đã đư<PERSON><PERSON> tạo thành công", "purchase.return.success.edit.message": "<PERSON><PERSON> cập nhật lợi nhuận mua hàng thành công", "purchase.return.success.delete.message": "<PERSON><PERSON> hàng trả lại đã đư<PERSON><PERSON> xóa thành công", "purchases.return.details.title": "<PERSON> tiết T<PERSON><PERSON> lại <PERSON> hàng", "settings.title": "Cài đặt", "settings.system-settings.title": "<PERSON><PERSON><PERSON> đặt hệ thống", "settings.system-settings.select.default-currency.label": "mặc định ngoại tệ", "settings.system-settings.select.default-currency.placeholder.label": "<PERSON><PERSON><PERSON> đơn vị tiền tệ mặc định", "settings.system-settings.input.default-email.label": "Email mặc định", "settings.system-settings.input.default-email.placeholder.label": "<PERSON><PERSON><PERSON><PERSON>ail Mặc định", "settings.system-settings.select.default-language.label": "<PERSON><PERSON><PERSON> ngữ mặc định", "settings.system-settings.select.default-language.placeholder.label": "<PERSON><PERSON><PERSON> ngôn ngữ mặc định", "settings.system-settings.select.default-customer.label": "<PERSON><PERSON><PERSON><PERSON> hàng mặc định", "settings.system-settings.select.default-customer.placeholder.label": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng mặc định", "settings.system-settings.select.default-warehouse.label": "<PERSON><PERSON> mặc định", "settings.system-settings.select.default-warehouse.placeholder.label": "<PERSON><PERSON><PERSON> kho mặc định", "settings.system-settings.input.change-logo.label": "<PERSON>hay đổi biểu trưng", "settings.system-settings.input.company-name.label": "<PERSON><PERSON>n công ty", "settings.system-settings.input.company-name.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> tên công ty", "settings.system-settings.input.company-phone.label": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i công ty", "settings.system-settings.input.company-phone.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> số điện thoại công ty", "settings.system-settings.input.developed-by.label": "<PERSON><PERSON><PERSON><PERSON> phát triển bởi", "settings.system-settings.input.developed-by.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> phát triển bởi", "settings.system-settings.input.footer.label": "<PERSON><PERSON> trang", "settings.system-settings.input.footer.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> chân trang", "settings.payment-gateway.title": "<PERSON><PERSON><PERSON> thanh toán", "settings.payment-gateway.input.stripe-key.label": "STRIPE_KEY", "settings.payment-gateway.input.stripe-key.placeholder.label": "<PERSON>ui lòng để trống trường này nếu bạn chưa thay đổi nó", "settings.payment-gateway.input.stripe-secret.label": "STRIPE_SECRET", "settings.payment-gateway.switch-btn.label": "Xóa các khóa API sọc", "settings.sms-configuration.title": "<PERSON><PERSON><PERSON>", "settings.sms-configuration.select.sms-gateway.label": "Cổng SMS", "settings.sms-configuration.select.sms-gateway.placeholder.label": "Chọn SMS Gateway", "settings.sms-configuration.input.twilio-sid.label": "TWILIO SID", "settings.sms-configuration.input.twilio-token.label": "TWILIO_TOKEN", "settings.sms-configuration.select.twilio-from.label": "TWILIO_FROM", "settings.sms-configuration.select.twilio-from.placeholder.label": "Nhập TWILIO FROM", "settings.sms-configuration.input.twilio-sid.placeholder.label": "Nhập TWILIO SID", "settings.smtp-configuration.title": "<PERSON><PERSON><PERSON> h<PERSON>nh SM<PERSON>", "settings.smtp-configuration.input.host.label": "CHỦ NHÀ", "settings.smtp-configuration.input.port.label": "HẢI CẢNG", "settings.smtp-configuration.input.username.label": "tên tà<PERSON>n", "settings.smtp-configuration.input.password.label": "<PERSON><PERSON><PERSON>", "settings.smtp-configuration.input.encryption.label": "Mã hóa", "settings.clear-cache.title": "Xóa bộ nhớ cache", "settings.system-settings.select.default-currency.validate.label": "<PERSON><PERSON> lòng chọn đơn vị tiền tệ", "settings.system-settings.input.company-name.validate.label": "<PERSON><PERSON> lòng nhập tên công ty", "settings.system-settings.input.company-phone.validate.label": "<PERSON><PERSON> lòng nhập số điện thoại của công ty", "settings.system-settings.input.developed-by.validate.label": "<PERSON><PERSON> lòng nhập đư<PERSON> phát triển bởi", "settings.system-settings.input.footer.validate.label": "vui lòng nhập thành phố", "settings.system-settings.select.default-language.validate.label": "<PERSON><PERSON> lòng chọn ngôn ngữ", "settings.system-settings.select.default-customer.validate.label": "<PERSON><PERSON> lòng chọn kh<PERSON>ch hàng", "settings.system-settings.select.default-warehouse.validate.label": "<PERSON><PERSON> lòng chọn kho", "settings.system-settings.select.address.validate.label": "<PERSON><PERSON> lòng nhập địa chỉ", "settings.system-settings.select.address.valid.validate.label": "Địa chỉ không đư<PERSON>c lớn hơn 150 ký tự", "settings.sms-configuration.select.sms-gateway.validate.label": "<PERSON><PERSON> lòng chọn cổng sms", "settings.sms-configuration.input.twilio-sid.validate.label": "<PERSON><PERSON> lòng nh<PERSON>p sid", "settings.sms-configuration.input.twilio-token.validate.label": "<PERSON><PERSON> lòng nhập mã thông báo", "settings.sms-configuration.select.twilio-from.validate.label": "<PERSON><PERSON> lòng nhập twillo từ", "settings.smtp-configuration.input.host.validate.label": "<PERSON><PERSON> lòng nhập máy chủ smtp", "settings.smtp-configuration.input.port.validate.label": "<PERSON><PERSON> lòng nhập cổng smtp", "settings.smtp-configuration.input.username.validate.label": "<PERSON><PERSON> lòng nhập tên người dùng", "settings.smtp-configuration.input.password.validate.label": "<PERSON>n vui lòng nhập mật kh<PERSON>u", "settings.smtp-configuration.input.encryption.validate.label": "<PERSON><PERSON> lòng nhập mã hóa", "settings.success.edit.message": "<PERSON><PERSON> cập nhật cài đặt thành công", "update-profile.input.full-name.label": "Họ và tên", "update-profile.title": "<PERSON> ti<PERSON><PERSON> hồ sơ", "update-profile.tab.title": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> hồ sơ", "update-profile.success.update.message": "<PERSON><PERSON> sơ đư<PERSON><PERSON> cập nhật thành công", "change-password.input.current.label": "<PERSON><PERSON><PERSON><PERSON> hiện tại", "change-password.input.new.label": "mật kh<PERSON>u mới", "change-password.input.confirm.label": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u", "change-password.input.current.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u hiện tại", "change-password.input.new.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u mới", "change-password.input.confirm.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>c nhận mật kh<PERSON>u", "change-password.input.current.validate.label": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u hiện tại", "change-password.input.new.validate.label": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u mới", "change-password.input.confirm.validate.label": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u x<PERSON>c nhận", "change-password.input.confirm.valid.validate.label": "<PERSON><PERSON><PERSON> nh<PERSON>n mật khẩu không khớp", "login-form.title": "<PERSON><PERSON><PERSON>", "login-form.login-btn.label": "<PERSON><PERSON><PERSON>", "login-form.forgot-password.label": "<PERSON>uên mật khẩu ?", "forgot-password-form.reset-link-btn.label": "<PERSON><PERSON><PERSON> liên kết đặt lại mật khẩu", "forgot-password-form.success.reset-link.label": "<PERSON><PERSON>g tôi đã gửi qua email liên kết đặt lại mật khẩu của bạn!", "login.success.message": "<PERSON><PERSON><PERSON> nhập thành công.", "logout.success.message": "<PERSON><PERSON><PERSON> xuất thành công.", "change-language.update.success.message": "<PERSON><PERSON> cập nhật ngôn ngữ thành công", "reset-password.title": "Đặt lại mật khẩu", "reset-password.password.validate.label": "<PERSON><PERSON>t khẩu xác nhận và mật khẩu phải khớp", "reset-password.success.update.message": "<PERSON><PERSON>t khẩu của bạn đã đ<PERSON><PERSON><PERSON> thiết lập lại!", "sales.title": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "sale.title": "<PERSON><PERSON>h thu", "sale.create.title": "<PERSON><PERSON><PERSON> b<PERSON> hàng", "sale.edit.title": "Chỉnh sửa giảm giá", "sale.select.customer.label": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>ng", "sale.select.customer.placeholder.label": "<PERSON><PERSON><PERSON> kh<PERSON> h<PERSON>ng", "sale.select.customer.validate.label": "<PERSON><PERSON> lòng chọn kh<PERSON>ch hàng", "sale.select.payment-status.placeholder": "<PERSON><PERSON><PERSON> thái <PERSON> toán", "sale.order-item.table.net-unit-price.column.label": "Đơn gi<PERSON> ròng", "sale.product.table.no-data.label": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "sale.success.create.message": "<PERSON><PERSON><PERSON><PERSON> giá đã đư<PERSON><PERSON> tạo thành công", "sale.success.edit.message": "<PERSON><PERSON> cập nhật ưu đãi thành công", "sale.success.delete.message": "<PERSON><PERSON><PERSON><PERSON> giá đã đư<PERSON>c xóa thành công", "sale.details.title": "<PERSON> ti<PERSON><PERSON> b<PERSON> hàng", "sale.detail.customer.info": "thông tin khách hàng", "sale.detail.invoice.info": "<PERSON>h<PERSON><PERSON> tin hóa đơn", "sales-return.title": "<PERSON><PERSON><PERSON> b<PERSON> hàng", "sale-return.title": "<PERSON><PERSON> hàng trở lại", "sale-return.create.title": "<PERSON><PERSON><PERSON> lợ<PERSON> n<PERSON>n b<PERSON> hàng", "sale-return.edit.title": "Chỉnh sửa lợi nhu<PERSON>n bán hàng", "sale-return.success.create.message": "<PERSON><PERSON> tạo lợi nhuận bán hàng thành công", "sale-return.success.edit.message": "<PERSON><PERSON> cập nhật lợi nhuận bán hàng thành công", "sale-return.success.delete.message": "<PERSON><PERSON> xóa lợi nhuận bán hàng thành công", "sale-return.details.title": "<PERSON> tiết bán hàng trở lại", "date-picker.filter.today.label": "<PERSON><PERSON><PERSON> nay", "date-picker.filter.this-week.label": "<PERSON><PERSON><PERSON>", "date-picker.filter.last-week.label": "<PERSON><PERSON><PERSON> tr<PERSON>", "date-picker.filter.this-month.label": "<PERSON><PERSON><PERSON><PERSON>", "date-picker.filter.last-month.label": "<PERSON><PERSON><PERSON><PERSON>", "date-picker.filter.Custom-Range.label": "Phạm vi tùy chỉnh", "date-picker.filter.reset.label": "<PERSON><PERSON>i lại", "date-picker.filter.apply.label": "Ứng dụng", "date-picker.filter.placeholder.label": "<PERSON><PERSON><PERSON>", "bar.title": "<PERSON><PERSON><PERSON> ba", "line.title": "<PERSON><PERSON><PERSON>", "filter.label": "<PERSON><PERSON><PERSON>", "reports.title": "Báo cáo", "warehouse.reports.title": "<PERSON><PERSON>o c<PERSON>o kho hàng", "sale.reports.title": "<PERSON><PERSON><PERSON> c<PERSON>o b<PERSON> h<PERSON>ng", "stock.reports.title": "Báo c<PERSON>o ch<PERSON> k<PERSON>", "purchase.reports.title": "Báo c<PERSON>o mua hàng", "top-selling-product.reports.title": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> phẩm <PERSON><PERSON> ch<PERSON>y nhất", "globally.react-table.column.code.label": "Mã số", "print.barcode.title": "In mã vạch", "paper.size.title": "<PERSON><PERSON><PERSON> gi<PERSON>y", "paper.size.placeholder.label": "<PERSON><PERSON><PERSON> k<PERSON> gi<PERSON>y", "globally.paper.size.validate.label": "<PERSON><PERSON><PERSON> k<PERSON> gi<PERSON>y", "print.validate.label": "<PERSON><PERSON> lòng cập nhật mã vạch để in", "current.stock.label": "<PERSON><PERSON> phiếu hiện tại", "stock.report.details.title": "<PERSON> tiết <PERSON> c<PERSON>o <PERSON> phiếu", "print.title": "In", "update.title": "<PERSON><PERSON><PERSON>", "preview.title": "<PERSON><PERSON>", "toast.successful.title": "<PERSON><PERSON><PERSON><PERSON> công", "toast.error.title": "Đã xảy ra lỗi!", "unit.filter.all.label": "<PERSON><PERSON><PERSON> c<PERSON> các", "unit.filter.piece.label": "<PERSON><PERSON><PERSON>", "unit.filter.meter.label": "<PERSON><PERSON><PERSON>", "unit.filter.kilogram.label": "Ki-l<PERSON>-gam", "status.filter.received.label": "Nhậ<PERSON>", "status.filter.pending.label": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> quy<PERSON>", "status.filter.ordered.label": "<PERSON><PERSON> đặt hàng", "payment-status.filter.paid.label": "Trả", "payment-status.filter.unpaid.label": "<PERSON><PERSON><PERSON> to<PERSON>", "excel.btn.label": "EXCEL", "pdf.btn.label": "PDF", "cash.label": "Tiền mặt", "no-option.label": "<PERSON><PERSON><PERSON><PERSON> có lựa chọn", "select.payment-type.label": "<PERSON><PERSON><PERSON> thức thanh toán", "payment-type.filter.cheque.label": "<PERSON><PERSON><PERSON> tra", "payment-type.filter.bank-transfer.label": "Chuy<PERSON>n kho<PERSON>n ngân hàng", "payment-type.filter.other.label": "K<PERSON><PERSON><PERSON>", "paying-amount-title": "<PERSON><PERSON> tiền thanh toán", "create-payment-title": "<PERSON><PERSON><PERSON> thanh toán", "reference-placeholder-label": "<PERSON><PERSON><PERSON><PERSON> tài li<PERSON>u tham kh<PERSON>o", "input-Amount-to-pay-title": "Số tiền phải trả", "paying-amount-validate-label": "<PERSON><PERSON> tiền thanh toán phải nhỏ hơn hoặc bằng Số tiền phải trả", "edit-payment-title": "Chỉnh s<PERSON>a <PERSON> toán", "no-product-found.label": "<PERSON><PERSON><PERSON><PERSON> tìm thấy sản phẩm", "sale.select.payment-type.placeholder": "<PERSON><PERSON><PERSON> hình thức thanh toán", "globally.payment.type.validate.label": "<PERSON><PERSON> lòng chọn hình thức thanh toán", "pos.payment.amount.exceeds.total.error": "<PERSON><PERSON> tiền thanh toán không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá tổng số tiền", "pos.payment.total.exceeds.grand.total.error": "Tổng số tiền thanh toán vượt quá tổng cộng", "globally.payment.details.validate.label": "<PERSON>ui lòng kiểm tra chi tiết thanh toán để tìm lỗi", "product.quantity.alert.reports.title": "<PERSON><PERSON><PERSON> b<PERSON>o số lượng sản phẩm", "globally-saving-btn-label": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>...", "payment-status.filter.partial.label": "<PERSON><PERSON><PERSON>", "dashboard.recentSales.total-product.label": "Tổng số sản phẩm", "adjustments.title": "<PERSON><PERSON><PERSON><PERSON> chỉnh", "adjustments.create.title": "Tạo điều chỉnh", "adjustments.edit.title": "Chỉnh sửa điều chỉnh", "adjustments.detail.title": "<PERSON> tiết điều chỉnh", "Adjustment.success.create.message": "Điều chỉnh đã được tạo thành công", "Adjustment.success.edit.message": "<PERSON><PERSON> cập nhật điều chỉnh thành công", "Adjustment.success.delete.message": "Đã xóa điều chỉnh thành công", "login-form.go-to-sign-in.label": "Quay lại <PERSON>p", "pos-product.title": "SẢN PHẨM", "pos-qty.title": "QTY", "pos-price.title": "GIÁ BÁN", "pos-sub-total.title": "TỔNG SUB", "pos-total-qty.title": "Tổng QTY", "pos-total.title": "<PERSON><PERSON><PERSON> cộng", "pos-pay-now.btn": "<PERSON><PERSON> to<PERSON> ngay", "pos-globally.search.field.label": "<PERSON><PERSON><PERSON> / Tìm kiếm sản phẩm theo tên mã", "pos-make-Payment.title": "<PERSON><PERSON> toán", "pos-received-amount.title": "<PERSON><PERSON> tiền đã nhận", "pos-total-amount.title": "Tổng cộng", "pos-sale.detail.invoice.info": "<PERSON><PERSON><PERSON>", "pos-sale.detail.Phone.info": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i", "pos-sale.detail.Paid-bt.title": "<PERSON><PERSON><PERSON><PERSON> trả bởi", "pos-close-btn.title": "Đ<PERSON><PERSON>", "pos-item.print.invoice.title": "<PERSON><PERSON><PERSON>", "pos-all.categories.label": "<PERSON><PERSON><PERSON> cả danh mục", "pos-all.brands.label": "<PERSON><PERSON><PERSON> c<PERSON> các thư<PERSON> hi<PERSON>u", "pos-no-product-available.label": "<PERSON><PERSON><PERSON><PERSON> có sản phẩm nào khả dụng", "pos-sale.select.discount-type.placeholder": "<PERSON><PERSON><PERSON> lo<PERSON>i gi<PERSON>m giá", "pos-sale.select.sale-unit-type.placeholder": "<PERSON><PERSON><PERSON> loại đơn vị bán hàng", "pos-thank.you-slip.invoice": "Cảm ơn đã đi mua sắm với chúng tôi. <PERSON><PERSON> lòng truy cập lại", "pos.payment.success.message": "<PERSON><PERSON> <PERSON><PERSON> đ<PERSON><PERSON><PERSON> thực hiện thành công", "discount-type.filter.percentage.label": "<PERSON><PERSON><PERSON> tr<PERSON>m", "discount-type.filter.fixed.label": "<PERSON><PERSON> sửa", "tax-type.filter.exclusive.label": "Lo<PERSON><PERSON> trừ", "tax-type.filter.inclusive.label": "<PERSON><PERSON>", "pos.cash-payment.product-error.message": "<PERSON><PERSON> lòng thêm sản phẩm vào giỏ hàng", "pos.cash-payment.quantity-error.message": "<PERSON><PERSON> lòng thêm số lượng sản phẩm", "pos.cash-payment.tax-error.message": "<PERSON><PERSON> lòng nh<PERSON>p giá trị thuế từ 0 đến 100", "pos.cash-payment.total-amount-error.message": "Số tiền chiết khấu không đư<PERSON><PERSON> lớn hơn tổng số", "pos.subtotal.small.title": "Tổng phụ", "settings.system-settings.select.default-version-footer.placeholder.label": "Hi<PERSON>n thị số phiên bản ở chân trang", "settings.system-settings.select.logo.placeholder.label": "Hiển thị logo trong phiếu thanh toán", "settings.system-settings.select.appname-sidebar.placeholder.label": "<PERSON><PERSON>n thị tên ứng dụng trong thanh bên", "pos.cash-payment.sub-total-amount-error.message": "Số tiền vận chuyển không đư<PERSON><PERSON> lớn hơn tổng số phụ", "product.import.title": "<PERSON><PERSON><PERSON><PERSON> sản ph<PERSON>m", "globally.sample.download.label": "<PERSON><PERSON><PERSON> xuống mẫu", "product-code.import.required-highlight.message": "mã phải không tồn tại rồi", "product-unit.import.required-highlight.message": "đơn vị phải đã đư<PERSON><PERSON> tạo Vui lòng sử dụng tên đầy đủ của đơn vị", "globally.optional-input.validate.label": "<PERSON><PERSON><PERSON><PERSON><PERSON> tù<PERSON>n", "reset.title": "<PERSON><PERSON>i lại", "reset.yes.title": "<PERSON><PERSON>, Đặt lại", "dashboard.widget.today-total-purchases.label": "Tổng số lần mua hàng hôm nay", "dashboard.widget.today-payment-received.label": "<PERSON><PERSON><PERSON> số nhận đ<PERSON><PERSON><PERSON> h<PERSON> (Doanh số bán hà<PERSON>)", "dashboard.widget.today-total-sales.label": "<PERSON><PERSON>ng doanh số hôm nay", "dashboard.widget.today-total-expense.label": "Tổng chi phí hôm nay", "reset.modal.msg": "Bạn có chắc chắn muốn đặt lại không", "globally.file.validate.label": "<PERSON><PERSON> lòng chọn tệp", "globally.csv-file.validate.label": "<PERSON><PERSON> lòng chọn tệp csv", "file.success.upload.message": "<PERSON><PERSON><PERSON> tài liệu thành công", "transfers.title": "<PERSON><PERSON><PERSON><PERSON>", "transfer.title": "<PERSON><PERSON><PERSON><PERSON>", "transfer.create.title": "<PERSON><PERSON><PERSON>", "transfer.edit.title": "Chỉnh sửa chuyển k<PERSON>n", "transfer.from-warehouse.title": "Từ nhà kho", "transfer.to-warehouse.title": "Đ<PERSON><PERSON> nh<PERSON> kho", "transfer.success.create.message": "<PERSON><PERSON> tạo chuyển khoản thành công", "transfer.success.edit.message": "<PERSON><PERSON> cập nhật chuyển khoản thành công", "transfer.success.delete.message": "<PERSON><PERSON> xóa chuyển thành công", "transfer.select.warehouse.validate.message": "Bạn không thể chuyển hàng trong cùng một kho", "status.filter.complated.label": "So sánh", "status.filter.sent.label": "Gởi", "transfer.details.title": "<PERSON> tiết chuyển k<PERSON>n", "settings.prefixes-settings.input.purchases.placeholder.label": "PU", "settings.prefixes-settings.input.purchases-return.placeholder.label": "PR", "settings.prefixes-settings.input.sales.placeholder.label": "SL", "settings.prefixes-settings.input.salse-return.placeholder.label": "SR", "settings.prefixes-settings.input.expense.placeholder.label": "VÍ DỤ", "settings.prefixes-settings.input.purchases.validate.label": "<PERSON><PERSON> lòng nh<PERSON>p giao dịch mua", "settings.prefixes-settings.input.purchases-return.validate.label": "<PERSON><PERSON> lòng nh<PERSON>p các giao dịch mua trả lại", "settings.prefixes-settings.input.sales.validate.label": "<PERSON><PERSON> lòng nh<PERSON>p doanh số bán hàng", "settings.prefixes-settings.input.salse-return.validate.label": "<PERSON><PERSON> lòng nhập lợi nhu<PERSON>n bán hàng", "settings.prefixes-settings.input.expense.validate.label": "<PERSON><PERSON> lòng nhập chi phí", "setting.state.lable": "<PERSON><PERSON><PERSON><PERSON> bang", "setting.postCode.lable": "PostCode", "settings.system-settings.select.state.validate.label": "<PERSON><PERSON> lòng chọn tiểu bang", "settings.system-settings.select.country.validate.label": "<PERSON><PERSON> lòng chọn quốc gia", "settings.system-settings.select.postcode.validate.label": "<PERSON><PERSON> lòng chọn mã bưu điện", "side-menu.empty.message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả", "product.export.title": "<PERSON><PERSON><PERSON> phẩm xuất kh<PERSON>u", "globally.input.content.label": "<PERSON><PERSON>i dung", "email-template.edit.title": "Chỉnh sửa Mẫu EMail", "email-template.title": "Mẫu thư điện tử", "email-template.success.edit.message": "<PERSON><PERSON> cập nhật thành công mẫu eail.", "pos.register-details.sell.title": "bán", "purchases.total.amount.title": "<PERSON><PERSON>ng số tiền mua hàng", "purchases-return.total.amount.title": "<PERSON><PERSON> hàng Trả lại Tổng số tiền", "supplier.report.details.title": "<PERSON> tiết <PERSON> cáo <PERSON>à cung cấp", "supplier.report.title": "Báo cáo nhà cung cấp", "prefix.title": "<PERSON><PERSON><PERSON><PERSON> tố", "quotations.title": "Báo giá", "create-quotation.title": "Tạo báo giá", "edit-quotation.title": "Chỉnh sửa Báo giá", "details-quotations.title": "<PERSON> tiết Báo giá", "quotation.success.create.message": "<PERSON><PERSON>o gi<PERSON> đ<PERSON><PERSON><PERSON> tạo thành công", "quotation.success.edit.message": "<PERSON><PERSON> cập nhật báo giá thành công", "quotation.success.delete.message": "<PERSON><PERSON> xóa báo giá thành công", "settings.system-settings.select.date-format.label": "<PERSON><PERSON><PERSON> dạng dữ liệu", "quotation.title": "Bảng báo giá", "quotation.detail.invoice.info": "Thông tin báo giá", "pepole.title": "<PERSON><PERSON> t<PERSON>c", "converted.status.label": "<PERSON><PERSON><PERSON><PERSON> đổi", "settings.system-settings.select.postcode.validate.length.label": "<PERSON><PERSON> dài mã bưu điện không đ<PERSON><PERSON><PERSON> hơn 8", "mail-settings.sender-name.title": "<PERSON><PERSON><PERSON><PERSON>i", "mail-settings.title": "Cài đặt Thư", "mail-settings.success.edit.message": "<PERSON><PERSON> cập nhật cài đặt thư thành công", "best-customer.report.title": "<PERSON><PERSON><PERSON><PERSON> hàng tốt nhất", "sms-template.edit.title": "Chỉnh sửa mẫu SMS", "sms-template.title": "Mẫu SMS", "sms-template.success.edit.message": "<PERSON><PERSON> cập nhật thành công mẫu SMS.", "sms-content-variables.title": "BIẾN NỘI DUNG SMS", "email-content-variables.title": "CÁC BIẾN ĐỔI NỘI DUNG EMAIL", "sms-content-text.error.message": "Bạn đã đạt đến số ký tự tối đa cho phép là 160.", "sms-content.error.message": "nội dung ph<PERSON><PERSON> đ<PERSON><PERSON><PERSON> yêu cầu", "profit-loss.reports.title": "<PERSON><PERSON><PERSON> lợ<PERSON>", "global.revenue.title": "<PERSON><PERSON>h thu", "global.gross-profit.title": "<PERSON><PERSON><PERSON>", "global.payment-received.title": "<PERSON><PERSON> nhận thanh toán", "global.payment-sent.title": "<PERSON><PERSON> gửi thanh toán", "global.net-payment.title": "Net thanh toán", "customer.report.details.title": "<PERSON> tiết <PERSON><PERSON> c<PERSON>o <PERSON> hàng", "customer.report.title": "<PERSON><PERSON>o c<PERSON>o kh<PERSON>ch hàng", "sale.payment.report.title": "<PERSON><PERSON> <PERSON><PERSON> bán hàng", "sale.total.amount.title": "<PERSON><PERSON><PERSON> số tiền bán hàng", "sale-return.total.amount.title": "Tổng số tiền trả lại hàng bán", "sale-Due.total.amount.title": "T<PERSON>ng số tiền đến hạn", "sale-paid.total.amount.title": "Tổng số tiền đã trả", "sale-reference.title": "<PERSON><PERSON> b<PERSON> h<PERSON>ng", "more-report.option.title": "H<PERSON><PERSON>", "currency.icon.right.side.lable": "<PERSON><PERSON><PERSON><PERSON> tượng tiền tệ Bên phải", "total.sales.title": "<PERSON><PERSON><PERSON> do<PERSON>h số", "email.status.edit.success.message": "<PERSON><PERSON> cập nhật trạng thái email thành công.", "sms.status.edit.success.message": "<PERSON><PERSON> cập nhật trạng thái SMS thành công.", "sms-api.title": "API SMS", "key.lable": "<PERSON><PERSON><PERSON>", "key.value.lable": "<PERSON><PERSON><PERSON> trị cốt lõi", "url.lable": "URL", "mobile.key.lable": "<PERSON><PERSON><PERSON><PERSON> di động", "message.key.lable": "<PERSON><PERSON><PERSON><PERSON> tin nh<PERSON>n", "sms.status.lable": "<PERSON><PERSON><PERSON>ng thái SMS", "active.status.lable": "<PERSON><PERSON><PERSON> c<PERSON>", "in-active.status.lable": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "sms.api.update.success.message": "SCập nhật API Sms Thành công.", "sale-return.product-qty.validate.message": "Số lượng trả lại nhiều hơn Số lượng đã bán", "template.title": "Mẫu", "pos.product-quantity-error.message": "<PERSON><PERSON><PERSON>ng còn số lượng có sẵn.", "product-list.lable": "<PERSON><PERSON> s<PERSON>ch sản ph<PERSON>m", "register.details.title": "<PERSON> tiết <PERSON> ký", "register.total-sales.label": "<PERSON><PERSON><PERSON> do<PERSON>h số", "register.total-return.title": "Tổng số tiền hoàn lại", "register.total-Payment.title": "<PERSON><PERSON>ng số tiền thanh toán", "register.product.sold.title": "Thông tin chi tiết về các sản phẩm đã bán", "register.product.sold.by.brand.title": "Thông tin chi tiết về sản phẩm đã bán (<PERSON>)", "print-barcode.show-company.label": "<PERSON><PERSON><PERSON> thị tên c<PERSON>a hàng", "print-barcode.show-product-name.label": "<PERSON><PERSON><PERSON> thị Tên <PERSON> ph<PERSON>m", "print-barcode.show-price.label": "<PERSON><PERSON><PERSON> thị <PERSON>", "product.input.quantity-limitation.label": "<PERSON><PERSON><PERSON><PERSON> hạn số lư<PERSON>", "product.input.quantity-limitation.placeholder": "<PERSON><PERSON><PERSON><PERSON> hạn <PERSON> l<PERSON>", "pos.hold-list-btn.title": "<PERSON><PERSON> chức", "create.hold-list.warning.message": "G<PERSON><PERSON> hóa đơn? Cùng tham khảo sẽ thay thế danh sách cũ nếu tồn tại !!", "create-modal.yes.ok-btn": "<PERSON>âng ok", "hold-list.reference-number.placeholder": "<PERSON>ui lòng nhập số tham chiếu!", "hold-list-id.table.column.label": "TÔI", "hold-list-ref-id.table.column.label": "Ref.ID", "hold-list.details.title": "<PERSON><PERSON><PERSON> danh s<PERSON>ch", "hold-list.success.create.message": "<PERSON><PERSON> tạo danh sách lưu giữ thành công", "hold-list.success.delete.message": "<PERSON><PERSON> xóa danh sách lưu giữ thành công", "report-all.warehouse.label": "<PERSON><PERSON><PERSON> cả kho hàng", "setting.mail-mailer.lable": "NGƯỜI BƯU ĐIỆN", "setting.mail-host.lable": "MÁY CHƯA THƯ", "setting.mail-port.lable": "CỔNG THƯ", "setting.mail-user-name.lable": "TÊN NGƯỜI DÙNG GỬI THƯ", "setting.mail-password.lable": "MẬT KHẨU THƯ", "setting.mail-encryption.lable": "MÃ HÓA THƯ", "sale.payment.create.success": "<PERSON><PERSON> to<PERSON> bán hàng đã đư<PERSON><PERSON> tạo thành công", "sale.payment.edit.success": "<PERSON><PERSON> cập nhật thanh toán bán hàng thành công", "hold-list.reference-code.error": "Trư<PERSON>ng mã tham chiếu là bắt buộc.", "settings.clear-cache.success.message": "xóa bộ nhớ cache thành công", "product.product-in-stock.label": "Trong kho", "product-items.label": "v<PERSON><PERSON> p<PERSON>m", "Payload.key.lable": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>ng", "base-units.title": "Đơn vị cơ sở", "base-unit.create.title": "<PERSON><PERSON>o đơn vị cơ sở", "base-unit.edit.title": "Chỉnh sửa đơn vị cơ sở", "base-unit.title": "Đơn vị cơ sở", "base-unit.success.create.message": "Đơn vị cơ sở đượ<PERSON> tạo thành công", "base-unit.success.edit.message": "Đơn vị cơ sở đư<PERSON><PERSON> cập nhật thành công", "base-unit.success.delete.message": "Đơn vị cơ sở đã được xóa thành công", "DOB.input.label": "DOB", "purchase.product.quantity.validate.label": "<PERSON><PERSON> lòng nhập số lượng sản phẩm", "languages.title": "ngôn ng<PERSON>", "react-data-table.translation.column.label": "<PERSON><PERSON><PERSON>", "react-data-table.iso-date.column.label": "Mã ISO", "globally.input.iso-code.validate.label": "<PERSON><PERSON> lòng nh<PERSON>p mã ISO", "globally.input.iso-code.character.validate.label": "<PERSON><PERSON> dài mã ISO phải bằng 2", "translation.manager.title": "<PERSON><PERSON><PERSON><PERSON> lý d<PERSON> thu<PERSON>", "language.updated.success.message": "<PERSON><PERSON> cập nhật ngôn ngữ thành công", "language.deleted.success.message": "<PERSON><PERSON> xóa ngôn ngữ thành công", "language.edit.success.message": "Đã chỉnh sửa ngôn ngữ thành công", "language.save.success.message": "<PERSON><PERSON><PERSON> ngữ đư<PERSON><PERSON> tạo thành công", "language.enabled.success.message": "<PERSON><PERSON> bật ngôn ngữ thành công", "language.disabled.success.message": "<PERSON><PERSON> tắt ngôn ngữ thành công", "language.current-language-disable.error.message": "Bạn không thể tắt ngôn ngữ hiện tại đang đư<PERSON><PERSON> chọn", "header.profile-menu.change-language.label": "<PERSON>hay đ<PERSON>i ngôn ngữ", "language.title": "<PERSON><PERSON><PERSON>", "pos-paying-amount.title": "số tiền thanh toán", "pos.change-return.label": "Đổi <PERSON>", "language.create.title": "<PERSON><PERSON><PERSON> ngôn ngữ", "purchase.less.recieving.ammout.error": "<PERSON><PERSON> tiền nhận nhỏ hơn Tổng số lớn.", "add-stock.title": "thêm ch<PERSON> k<PERSON>", "product-quantity.add.title": "<PERSON><PERSON><PERSON><PERSON> số lượng sản phẩm", "edit-translation.title": "Chỉnh s<PERSON>a bản d<PERSON>ch", "globally.input.cash-in-hand.label": "Tiền mặt trong tay", "globally.close-register.title": "<PERSON><PERSON><PERSON> đ<PERSON>ng ký", "register.closed.successfully.message": "<PERSON><PERSON><PERSON> ký đã đóng thành công.", "register.entry.added.successfully.message": "<PERSON><PERSON><PERSON><PERSON> mục đăng ký thành công.", "globally.total-cash.label": "Tổng tiền mặt", "globally.input.note.label": "<PERSON><PERSON><PERSON>", "globally.input.note.placeholder.label": "<PERSON><PERSON><PERSON><PERSON>", "register.report.title": "<PERSON><PERSON><PERSON> ký báo cáo", "user-details.table.opened-on.row.label": "Đã mở vào", "user-details.table.closde-on.row.label": "<PERSON><PERSON> đóng", "globally.input.cash-in-hand-while-closing.label": "Tiền mặt trong tay khi đóng cửa", "pos.cclose-register.enter-total-cash.message": "<PERSON><PERSON> lòng thêm tổng số tiền mặt.", "register.is.still.open.message": "<PERSON><PERSON>ng ký vẫn đang mở!!!", "Are.you.sure.you.want.to.go.to.dashboard.message": "Bạn có chắc muốn truy cập Trang tổng quan không?", "product.quantity.title": "S<PERSON> l<PERSON> sản phẩm", "pos.this.product.out.of.stock.message": "<PERSON><PERSON><PERSON> phẩm này đã hết hàng", "pos.quantity.exceeds.quantity.available.in.stock.message": "Số lượng vượt quá số lượng có sẵn trong kho", "yes.modal.title": "<PERSON><PERSON><PERSON>", "no.modal.title": "KHÔNG", "language.edit.title": "Chỉnh sửa ngôn ngữ", "select.user.label": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> dùng", "suppliers.import.title": "<PERSON><PERSON><PERSON> cung cấp nhập kh<PERSON>u", "customers.import.title": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>ng nh<PERSON> kh<PERSON>", "products.type.single-type.label": "Đơn", "product.type.label": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "product.type.placeholder.label": "<PERSON><PERSON><PERSON>n phẩm", "product.type.input.validation.error": "<PERSON><PERSON> lòng chọn lo<PERSON>i sản phẩm", "variation.name": "<PERSON><PERSON><PERSON> biến thể", "variation.variation_types": "<PERSON><PERSON><PERSON> biến thể", "variations.title": "<PERSON><PERSON><PERSON>n thể", "variation.title": "<PERSON><PERSON><PERSON><PERSON> thể", "variation.create.title": "<PERSON><PERSON><PERSON> biến thể", "variation.edit.title": "Chỉnh sửa biến thể", "variation.input.name.label": "<PERSON><PERSON><PERSON>", "variation.input.name.placeholder.label": "<PERSON><PERSON><PERSON><PERSON> tên", "variation.input.name.validate.label": "<PERSON><PERSON> lòng nhập tên", "variation.success.create.message": "<PERSON><PERSON><PERSON> biến thể thành công", "variation.success.edit.message": "Chỉnh sửa biến thể thành công", "variation.success.delete.message": "<PERSON><PERSON><PERSON> biến thể thành công", "variation.types.title": "<PERSON><PERSON><PERSON> biến thể", "variation.type.title": "<PERSON><PERSON><PERSON> biến thể", "variation.type.input.name.placeholder.label": "<PERSON><PERSON> lòng nhập lo<PERSON>i biến thể", "variation.type.input.name.validate.label": "<PERSON><PERSON> lòng nhập lo<PERSON>i biến thể hợp lệ", "variation.select.validation.error.message": "<PERSON><PERSON> lòng chọn một biến thể", "variation.type.select.validate.error.message": "<PERSON><PERSON> lòng chọn loại biến thể", "products.warehouse.title": "<PERSON><PERSON> s<PERSON>n p<PERSON>m", "barcode-symbol-uppercase-validation-message": "<PERSON><PERSON> lòng chỉ nhập chữ in hoa và số cho mã vạch Mã 39.", "sale.product-qty.limit.validate.message": "<PERSON>ạn không thể mua nhiều hơn số lượng giới hạn", "receipt-settings.title": "Makbuz <PERSON>ları", "receipt-settings.show-warehouse.label": "<PERSON><PERSON><PERSON>", "receipt-settings.show-email.label": "E-postayı Göster", "receipt-settings.show-address.label": "<PERSON><PERSON><PERSON>", "receipt-settings.show-customer.label": "Müşteriyi Göster", "receipt-settings.show-phone.label": "Telefonu Göster", "receipt-settings.show-discount-shipping.label": "<PERSON><PERSON><PERSON> thị <PERSON> g<PERSON> & <PERSON><PERSON><PERSON> chuyển", "receipt-settings.success.edit.message": "Makbuz ayarı başarıyla güncellendi", "receipt-settigns.input.note.validate.label": "<PERSON>ü<PERSON>fen notu girin", "receipt-settings.show-barcode.label": "<PERSON><PERSON><PERSON> thị mã vạch trong biên nhận", "receipt-settings.show-note.label": "<PERSON><PERSON><PERSON> thị ghi chú", "globally.submit-and-print-button": "<PERSON><PERSON><PERSON> và in", "receipt-settings.show-product-code.label": "<PERSON><PERSON>n thị mã sản phẩm", "globally.footer.label": "<PERSON><PERSON><PERSON> quyền đ<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "logout.confirmation.label": "Bạn có chắc chắn muốn Đăng xuất không?", "addition.title": "<PERSON><PERSON><PERSON> c<PERSON>", "subtraction.title": "<PERSON><PERSON><PERSON> trừ", "print-custom-barcode.title": "In mã vạch tùy chỉnh", "product.sku.label": "SKU/Mã vạch", "add.stock.while.product.creation.title": "<PERSON>h<PERSON><PERSON> hàng trong khi tạo sản phẩm", "confirm-modal.msg": "Bạn có chắc không?", "store.title": "<PERSON><PERSON><PERSON> h<PERSON>", "create.store.title": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> hàng", "edit.store.title": "Chỉnh sửa cửa hàng", "store.name.title": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> hàng", "store.name.placeholder.title": "<PERSON><PERSON><PERSON><PERSON> tên c<PERSON><PERSON> hàng", "store.name.validate.label": "<PERSON><PERSON> lòng nhập tên cửa hàng", "store.success.create.message": "<PERSON><PERSON> tạo cửa hàng thành công", "store.success.edit.message": "<PERSON><PERSON><PERSON> hàng đã cập nhật thành công", "store.success.delete.message": "<PERSON><PERSON> x<PERSON>a cửa hàng thành công", "store.changed.message": "<PERSON><PERSON><PERSON> hàng đã thay đổi thành công", "store.header.name.title": "<PERSON><PERSON><PERSON> h<PERSON>", "select.all.store.title": "<PERSON><PERSON><PERSON> tất cả cửa hàng", "store.field.must.required.validate": "<PERSON><PERSON><PERSON><PERSON><PERSON> cửa hàng phải là bắt buộc", "store.assigned.title": "<PERSON><PERSON><PERSON> hàng đã được chỉ định", "no.store.title": "<PERSON><PERSON><PERSON><PERSON> có thông tin cửa hàng", "paid.amount.title": "Số tiền đã thanh toán", "taxes.title": "<PERSON><PERSON><PERSON>", "tax.title": "<PERSON><PERSON><PERSON>", "add.tax.title": "<PERSON><PERSON><PERSON><PERSON> thuế", "edit.tax.title": "Chỉnh sửa thuế", "tax.name.title": "<PERSON><PERSON><PERSON> thuế", "tax.name.placeholder.title": "<PERSON><PERSON><PERSON><PERSON> tên thuế", "tax.name.validate.title": "<PERSON><PERSON> lòng nhập tên thuế", "tax.value.title": "<PERSON><PERSON><PERSON> trị thuế", "tax.value.placeholder.title": "<PERSON><PERSON><PERSON><PERSON> giá trị thuế", "tax.value.validate.title": "<PERSON><PERSON> lòng nhập giá trị thuế", "tax.deleted.success.message": "<PERSON><PERSON> xóa thuế thành công", "tax.edit.success.message": "Đã chỉnh sửa thuế thành công", "tax.save.success.message": "<PERSON><PERSON> tạo thuế thành công", "tax.name.unique.validate.title": "<PERSON><PERSON>n thuế đã tồn tại", "tax.value.unique.validate.title": "<PERSON><PERSON><PERSON> trị thuế đã tồn tại", "tax.show.on.receipt.pdf.title": "<PERSON><PERSON><PERSON> thị trên biên lai/PDF", "pos.settings.title": "Cài đặt POS", "enable.pos.sound.title": "<PERSON><PERSON><PERSON> âm thanh nh<PERSON>p chuột POS", "show.out.of.stock.product.in.pos": "<PERSON><PERSON><PERSON> thị sản phẩm hết hàng trong POS", "pos.sound.title": "Âm thanh POS", "upload.audio.title": "<PERSON><PERSON><PERSON> lên <PERSON>", "pos.audio.required": "<PERSON><PERSON> lòng tải lên âm <PERSON>h", "date.of.birth.title": "<PERSON><PERSON><PERSON>", "customer.details.title": "<PERSON> tiết kh<PERSON>ch hàng", "pos.audio.length.tooltip.title": "<PERSON><PERSON> dài âm thanh phải nhỏ hơn 3 giây.", "select.date.of.birth": "<PERSON><PERSON><PERSON> ng<PERSON>y sinh của bạn", "item.deleted.success.message": "<PERSON><PERSON> xóa mục thành công", "payment.method.save.success.message": "<PERSON><PERSON> tạo ph<PERSON><PERSON><PERSON> thức thanh toán thành công", "payment.method.edit.success.message": "<PERSON><PERSON> chỉnh sửa ph<PERSON><PERSON><PERSON> thức thanh toán thành công", "payment.method.deleted.success.message": "<PERSON><PERSON> xóa ph<PERSON><PERSON><PERSON> thức thanh toán thành công", "payment.method.name.unique.validate.title": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> thức thanh toán đã tồn tại", "show.tax.title": "<PERSON><PERSON><PERSON> th<PERSON>", "expiry.date.title": "<PERSON><PERSON><PERSON> h<PERSON> hạn", "expiry.date.placeholder.title": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON> hế<PERSON> hạn", "payment.method.title": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "dual.screen.settings.title": "Cài đặt màn hình kép", "dual.screen.display.header.title": "<PERSON>i<PERSON><PERSON> đề hiển thị", "dual.screen.display.header.placeholder.title": "<PERSON><PERSON><PERSON><PERSON> tiêu đề hiển thị", "please.enter.display.header.title": "<PERSON><PERSON> lòng nhập tiêu đề hiển thị", "carousel.image.title": "Hình ảnh vòng quay", "validation.you.can.upload.maximum.images": "<PERSON><PERSON>n có thể tải lên tối đa 5 hình <PERSON>nh", "upload.maximum.images": "<PERSON><PERSON><PERSON> lên tối đa năm hình <PERSON>nh.", "no.customer.selected.title": "<PERSON><PERSON><PERSON><PERSON> có khách hàng nào đ<PERSON><PERSON><PERSON> chọn", "send.test.email.title": "<PERSON><PERSON><PERSON> tra", "send.test.email.success.message": "<PERSON><PERSON> g<PERSON>i email thử nghiệm thành công.", "globally.receipt.download.label": "<PERSON><PERSON><PERSON> xu<PERSON>ng biên lai", "load.more.title": "<PERSON><PERSON><PERSON>ê<PERSON>", "pos.edit.sale.title": "Bạn có chắc chắn muốn chỉnh sửa đợt bán này không?", "sale.payment.total-exceed.validate.message": "Tổng số tiền thanh toán không đ<PERSON><PERSON><PERSON> vư<PERSON><PERSON> quá tổng số tiền.", "globally.input.width.label": "<PERSON><PERSON><PERSON> r<PERSON>", "globally.input.height.label": "<PERSON><PERSON><PERSON> cao", "globally.input.width.validate.label": "<PERSON><PERSON> lòng nh<PERSON>p chi<PERSON>u rộng", "globally.input.height.validate.label": "<PERSON><PERSON> lòng nh<PERSON>p chi<PERSON>u cao"}