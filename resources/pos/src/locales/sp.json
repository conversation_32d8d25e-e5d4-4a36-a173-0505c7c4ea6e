{"dashboard.title": "<PERSON><PERSON>", "header.pos.title": "TPV", "header.profile-menu.profile.label": "Perfil", "header.profile-menu.change-password.label": "Cambia la contraseña", "header.profile-menu.logout.label": "<PERSON><PERSON><PERSON>", "product.categories.title": "Categorías de Producto", "expense.categories.title": "Categorías de gastos", "dashboard.salesReturn.title": "Devolución de ventas", "dashboard.top-customers.title": "Los 5 mejores clientes", "dashboard.purchaseReturn.title": "Devolución de Compras", "dashboard.ThisWeekSales&Purchases.title": "Ventas y compras de esta semana", "dashboard.TopSellingProducts.title": "Productos más vendidos", "dashboard.stockAlert.title": "Alerta de existencias", "dashboard.recentSales.title": "Ventas Recientes", "dashboard.PaymentSentReceived.title": "Pago enviado y recibido", "dashboard.stockAlert.code.label": "Código", "dashboard.stockAlert.product.label": "Producto", "dashboard.stockAlert.warehouse.label": "<PERSON><PERSON><PERSON><PERSON>", "dashboard.stockAlert.quantity.label": "Cantidad", "dashboard.stockAlert.alertQuantity.label": "Cantidad de alerta", "dashboard.grantTotal.label": "Gran total", "dashboard.recentSales.reference.label": "Referencia", "dashboard.recentSales.customer.label": "Cliente", "dashboard.recentSales.status.label": "Estado", "dashboard.recentSales.paid.label": "<PERSON><PERSON>", "dashboard.recentSales.due.label": "<PERSON><PERSON><PERSON>", "dashboard.recentSales.paymentStatus.label": "Estado de pago", "settings.select.language.label": "Idioma", "settings.select.language.placeholder": "Seleccione El Idioma", "users.title": "Usuarios", "user.create.title": "<PERSON><PERSON><PERSON> usuario", "user.edit.title": "editar usuario", "user.input.first-name.label": "Primer nombre", "user.input.last-name.label": "Apellido", "user.input.email.label": "Correo electrónico", "user.input.phone-number.label": "Número de teléfono", "user.input.password.label": "Clave", "user.input.confirm-password.label": "<PERSON><PERSON>", "user.input.role.label": "Role", "user.input.first-name.placeholder.label": "Ingrese el nombre", "user.input.last-name.placeholder.label": "Introduzca el apellido", "user.input.email.placeholder.label": "Ingrese correo electrónico", "user.input.phone-number.placeholder.label": "Introduzca el número de teléfono", "user.input.password.placeholder.label": "Introducir la contraseña", "user.input.confirm-password.placeholder.label": "Ingresar Con<PERSON><PERSON><PERSON>", "user.input.role.placeholder.label": "<PERSON><PERSON>", "users.table.user.column.title": "Usuario", "users.table.phone-number.column.title": "Número de teléfono", "users.table.role.column.title": "Role", "users.table.date.column.title": "Fecha de creación", "user-details.title": "Detalles de usuario", "user-details.table.created-on.row.label": "Creado en", "user-details.table.title": "Visión de conjunto", "user.input.first-name.validate.label": "Por favor, introduzca el nombre", "user.input.last-name.validate.label": "Por favor ingrese apellido", "user.input.email.validate.label": "Por favor, introduzca la dirección de correo electrónico", "user.input.email.valid.validate.label": "Por favor ingrese una dirección de correo electrónico válida", "user.input.phone-number.validate.label": "Por favor ingrese el número de teléfono", "user.input.password.validate.label": "Por favor, ingrese contraseña", "user.input.confirm-password.validate.label": "Por favor ingrese confirmar contraseña", "user.input.role.validate.label": "Por favor seleccione rol", "user.success.create.message": "Usuario creado con éxito", "user.success.edit.message": "Usuario actualizado con éxito", "user.success.delete.message": "Usuario eliminado exitosamente", "react-data-table.searchbar.placeholder": "Búsqueda", "react-data-table.action.column.label": "Acción", "react-data-table.date.column.label": "<PERSON><PERSON>", "react-data-table.no-record-found.label": "No hay registros para mostrar", "react-data-table.records-per-page.label": "Registros por página", "dataTable.searchBar.placeholder.label": "Búsqueda", "delete-modal.title": "Borrar!", "delete-modal.msg": "¿Estás seguro de que quieres eliminar esto?", "delete-modal.yes-btn": "S<PERSON>, <PERSON><PERSON><PERSON>!", "delete-modal.no-btn": "no, cancelar", "suppliers.title": "<PERSON>veed<PERSON>", "supplier.title": "<PERSON><PERSON><PERSON><PERSON>", "supplier.create.title": "<PERSON><PERSON><PERSON> proveedor", "supplier.edit.title": "<PERSON><PERSON>", "supplier.table.name.column.title": "Nombre", "supplier.table.phone-number.column.title": "Número de teléfono", "supplier.table.email.column.title": "Correo electrónico", "supplier.table.address.column.title": "Dirección", "supplier.success.create.message": "Proveed<PERSON> creado con éxito", "supplier.success.edit.message": "Proveedor actualizado correctamente", "supplier.success.delete.message": "Proveedor eliminado con éxito", "globally.input.name.label": "Nombre", "globally.input.name.placeholder.label": "Ingrese su nombre", "globally.react-table.column.created-date.label": "Creado en", "globally.react-table.column.payment-type.label": "Tipo de pago", "globally.input.email.label": "Correo electrónico", "globally.input.email.placeholder.label": "Ingrese correo electrónico", "globally.input.phone-number.label": "Número de teléfono", "globally.input.phone-number.placeholder.label": "Introduzca el número de teléfono", "globally.input.country.label": "<PERSON><PERSON>", "globally.input.country.placeholder.label": "Introducir país", "globally.input.city.label": "Ciudad", "globally.input.city.placeholder.label": "Ingresar ciudad", "globally.input.address.label": "Dirección", "globally.input.address.placeholder.label": "Ingresa la direccion", "globally.input.notes.label": "notas", "globally.input.notes.placeholder.label": "Introducir notas", "globally.loading.label": "<PERSON>spere por favor...", "globally.input.name.validate.label": "Por favor ingrese el nombre", "globally.input.email.validate.label": "Por favor, introduzca la dirección de correo electrónico", "globally.input.email.valid.validate.label": "Por favor ingrese una dirección de correo electrónico válida", "globally.input.country.validate.label": "Por favor ingrese el país", "globally.input.city.validate.label": "Por favor ingrese la ciudad", "globally.input.phone-number.validate.label": "Por favor ingrese el número de teléfono", "globally.input.address.validate.label": "Por favor ingrese la dirección", "globally.input.notes.validate.label": "Las notas no deben tener más de 100 caracteres.", "globally.require-input.validate.label": "Este campo es obligatorio", "globally.date.validate.label": "Por favor ingrese la fecha", "globally.tax-length.validate.label": "El Impuesto no debe ser mayor a 100", "globally.discount-length.validate.label": "El descuento no debe ser mayor a 100", "globally.discount-cost-length.validate.label": "El descuento no debe ser mayor que el costo del producto", "globally.discount-cost-price.validate.label": "El descuento no debe ser mayor que el precio del producto", "globally.type.label": "Escribe", "globally.back-btn": "Atrás", "globally.save-btn": "Guardar", "globally.cancel-btn": "<PERSON><PERSON><PERSON>", "globally.edit-btn": "<PERSON><PERSON>", "globally.submit-btn": "<PERSON><PERSON><PERSON>", "globally.edit.tooltip.label": "<PERSON><PERSON>", "globally.delete.tooltip.label": "Bo<PERSON>r", "globally.view.tooltip.label": "Vista", "globally.pdf.download.label": "Descargar PDF", "globally.product-quantity.validate.message": "Favor de agregar cantidad de producto", "globally.product-already-added.validate.message": "Este producto ya se agregó", "globally.status.validate.label": "Por favor seleccione estado", "globally.payment.status.validate.label": "Por favor, seleccione el estado de pago", "globally.detail.reference": "Referencia", "globally.detail.status": "Estado", "globally.detail.warehouse": "<PERSON><PERSON><PERSON><PERSON>", "globally.detail.payment.status": "Estado de pago", "globally.detail.payment.details": "Detalles de pago", "globally.detail.company.info": "Información de la compañía", "globally.detail.order.summary": "Resumen del pedido", "globally.detail.product": "Producto", "globally.detail.net-unit-cost": "Costo unitario neto", "globally.detail.net-unit-price": "Precio Unitario Neto", "globally.detail.quantity": "Cantidad", "globally.detail.unit-cost": "Costo unitario", "globally.detail.unit-price": "Precio unitario", "globally.detail.discount": "Descuento", "globally.detail.tax": "Impuesto", "globally.detail.subtotal": "Total parcial", "globally.detail.order.tax": "Impuesto de pedido", "globally.show.payment.label": "Mostrar pagos", "globally.detail.shipping": "Envío", "globally.detail.grand.total": "Gran total", "globally.detail.paid": "Pagada", "globally.detail.due": "<PERSON><PERSON><PERSON>", "globally.search.field.label": "Buscar producto por nombre de código", "customers.title": "Clientes", "customer.title": "Cliente", "customer.create.title": "Crear cliente", "customer.edit.title": "Editar cliente", "customer.success.create.message": "Cliente creado con éxito", "customer.success.edit.message": "Cliente actualizado correctamente", "customer.success.delete.message": "Cliente eliminado exitosamente", "warehouse.title": "<PERSON><PERSON><PERSON><PERSON>", "warehouse.create.title": "<PERSON><PERSON><PERSON>", "warehouse.edit.title": "<PERSON><PERSON>", "warehouse.input.zip-code.label": "Código postal", "warehouse.input.zip-code.placeholder.label": "Ingresa tu código postal", "warehouse.input.zip-code.validate.label": "Por favor ingrese el código postal", "warehouse.input.zip-code.valid.validate.label": "Por favor ingrese un código postal válido", "warehouse.success.create.message": "Almacén creado con éxito", "warehouse.success.edit.message": "Almacén actualizado con éxito", "warehouse.success.delete.message": "Almacén eliminado con éxito", "products.title": "productos", "product.title": "Producto", "product.create.title": "Crear producto", "product.edit.title": "Editar producto", "product.input.code.label": "Código", "product.input.code.placeholder.label": "Introduzca el código", "product.input.product-category.label": "categoria de producto", "product.input.product-category.placeholder.label": "Elija la categoría del producto", "product.input.brand.label": "<PERSON><PERSON>", "product.input.brand.placeholder.label": "Elija la marca", "product.input.barcode-symbology.label": "Simbología de código de barras", "product.input.barcode-symbology.placeholder.label": "Elija la simbología de código de barras", "product.input.product-cost.label": "Costo del producto", "product.input.product-cost.placeholder.label": "Ingrese el costo del producto", "product.input.product-price.label": "Precio del producto", "product.input.product-price.placeholder.label": "Ingrese el precio del producto", "product.input.product-unit.label": "Unidad de producto", "product.input.product-unit.placeholder.label": "Elija la unidad de producto", "product.input.sale-unit.label": "Unidad de venta", "product.input.sale-unit.placeholder.label": "Elija Unidad de Venta", "product.input.purchase-unit.label": "Unidad de compra", "product.input.purchase-unit.placeholder.label": "Elija la unidad de compra", "product.input.stock-alert.label": "Alerta de existencias", "product.input.stock-alert.placeholder.label": "Entrar alerta de stock", "product.input.order-tax.label": "Impuesto de pedido", "product.input.order-tax.placeholder.label": "Ingrese el impuesto del pedido", "product.input.order-tax.validate.label": "Por favor, introduzca el impuesto del pedido", "product.input.order-tax.valid.validate.label": "El impuesto no debe ser mayor a 100", "product.input.tax-type.label": "Tipo de impuesto", "tax-type.filter.exclusive.label": "Exclusivo", "tax-type.filter.inclusive.label": "Inclusivo", "product.input.tax-type.placeholder.label": "Elija el tipo de impuesto", "product.input.warehouse.placeholder.label": "<PERSON><PERSON>", "product.input.multiple-image.label": "<PERSON><PERSON>", "product.table.image.column.label": "Imagen", "product.table.price.column.label": "Precio", "product.product-details.title": "Detalles de producto", "product.product-details.code-product.label": "Código del producto", "product.product-details.category.label": "Categoría", "product.product-details.cost.label": "Costo", "product.product-details.unit.label": "Unidad", "product.product-details.tax.label": "Impuesto", "product.input.code.validate.label": "por favor ingrese el código", "product.input.product-category.validate.label": "Seleccione la categoría del producto", "product.input.brand.validate.label": "Seleccione la marca", "product.input.barcode-symbology.validate.label": "Seleccione la simbología del código de barras", "product.input.product-cost.validate.label": "Por favor ingrese el costo del producto", "product.input.product-price.validate.label": "Por favor ingrese el precio del producto", "product.input.product-unit.validate.label": "seleccione la unidad de producto", "product.input.sale-unit.validate.label": "Por favor seleccione unidad de venta", "product.input.purchase-unit.validate.label": "Seleccione la unidad de compra", "product.input.stock-alert.validate.label": "Por favor ingrese alerta de existencias", "product.input.tax-type.validate.label": "Seleccione el tipo de impuesto", "product.input.warehouse.validate.label": "Por favor seleccione almacén", "product.success.create.message": "Producto creado con éxito", "product.success.edit.message": "Producto actualizado con éxito", "product.success.delete.message": "Producto eliminado con éxito", "product.image.success.upload.message": "Imagen cargada con éxito", "product.image.success.delete.message": "Imagen eliminada con éxito", "brands.title": "<PERSON><PERSON>", "brand.title": "<PERSON><PERSON>", "brand.create.title": "Crear marca", "brand.edit.title": "Editar marca", "brand.input.code.label": "Código", "globally.input.change-logo.tooltip": "Cambiar logotipo", "globally.input.change-image.tooltip": "Cambiar Imagen", "brand.table.brand-name.column.label": "Nombre de la marca", "brand.table.product-count.column.label": "Cantidad de productos", "brand.input.name.valid.validate.label": "El nombre no debe tener más de 50 caracteres", "brand.success.create.message": "Marca creada con éxito", "brand.success.edit.message": "Marca actualizada correctamente", "brand.success.delete.message": "Marca eliminada con éxito", "product-categories.title": "Categorías de Producto", "product-category.title": "categoria de producto", "product-category.create.title": "Crear categoría de producto", "product-category.edit.title": "Editar categoría de producto", "product-category.success.create.message": "Categoría de producto Creado con éxito", "product-category.success.edit.message": "Categoría de producto actualizada con éxito", "product-category.success.delete.message": "Categoría de producto eliminada con éxito", "expense-categories.title": "Categorías de gastos", "expense-category.title": "Categoría de gastos", "expense-category.create.title": "Crear categoría de gastos", "expense-category.edit.title": "Editar categoría de gastos", "expense-category.success.create.message": "Categoría de gasto Creado con éxito", "expense-category.success.edit.message": "Categoría de gastos actualizada con éxito", "expense-category.success.delete.message": "Categoría de gastos eliminada con éxito", "expenses.title": "Gastos", "expense.title": "Gastos", "expense.create.title": "<PERSON><PERSON><PERSON> gasto", "expense.edit.title": "<PERSON><PERSON> gas<PERSON>", "expense.input.details.label": "Detalles", "expense.input.details.placeholder.label": "Ingrese los detalles", "expense.input.amount.label": "Monto", "expense.input.amount.placeholder.label": "Ingrese la cantidad", "expense.input.title.label": "T<PERSON><PERSON><PERSON>", "expense.input.title.validate.label": "Ingrese el título del gasto", "expense.input.title.placeholder.label": "Ingrese el título del gasto", "expense.input.warehouse.placeholder.label": "<PERSON><PERSON>", "expense.input.expense-category.placeholder.label": "Elija la categoría de gastos", "expense.input.warehouse.validate.label": "Por favor seleccione almacén", "expense.input.expense-category.validate.label": "Seleccione la categoría de gastos", "expense.input.amount.validate.label": "Por favor ingrese la cantidad", "expense.success.create.message": "Gasto creado con éxito", "expense.success.edit.message": "Gasto actualizado con éxito", "expense.success.delete.message": "Gasto eliminado con éxito", "roles.title": "roles", "roles.permissions.title": "Roles/Permisos", "role.title": "Role", "role.create.title": "Crear rol", "role.edit.title": "Editar rol", "role.select.all-permission.label": "Todos los permisos", "role.input.permission.label": "permisos", "role.input.name.validate.label": "Por favor ingrese el nombre", "role.input.name.valid.validate.label": "El nombre no debe tener más de 50 caracteres", "role.success.create.message": "Rol creado con éxito", "role.success.edit.message": "Rol actualizado con éxito", "role.success.delete.message": "Rol eliminado con éxito", "units.title": "Unidades", "unit.title": "Unidad", "unit.create.title": "Crear unidad", "unit.edit.title": "Editar unidad", "unit.modal.input.short-name.label": "Nombre corto", "unit.modal.input.short-name.placeholder.label": "Ingrese el nombre corto", "unit.modal.input.base-unit.label": "Unidad base", "unit.modal.input.base-unit.placeholder.label": "Elija la unidad base", "unit.modal.input.short-name.validate.label": "Por favor ingrese un nombre corto", "unit.modal.input.short-name.valid.validate.label": "El nombre corto no debe tener más de 50 caracteres.", "unit.modal.input.base-unit.validate.label": "Elija la unidad base", "unit.success.create.message": "Unidad creada con éxito", "unit.success.edit.message": "Unidad actualizada con éxito", "unit.success.delete.message": "Unidad eliminada con éxito", "currencies.title": "<PERSON><PERSON><PERSON>", "currency.title": "Divisa", "currency.create.title": "<PERSON><PERSON><PERSON> mon<PERSON>", "currency.edit.title": "<PERSON><PERSON>", "currency.modal.input.name.placeholder.label": "Ingrese el nombre de la moneda", "currency.modal.input.code.label": "Código", "currency.modal.input.code.placeholder.label": "Ingrese el código de moneda", "currency.modal.input.symbol.label": "Símbolo", "currency.modal.input.symbol.placeholder.label": "Introducir sí<PERSON><PERSON> de moneda", "currency.modal.input.name.validate.label": "Ingrese el nombre de la moneda", "currency.modal.input.code.validate.label": "Por favor ingrese el código de moneda", "currency.modal.input.code.valid.validate.label": "El código no debe tener más de 20 caracteres", "currency.modal.input.symbol.validate.label": "Por favor ingrese el símbolo de la moneda", "currency.success.create.message": "Moneda creada con éxito", "currency.success.edit.message": "Moneda actualizada con éxito", "currency.success.delete.message": "Moneda eliminada con éxito", "purchases.title": "compras", "purchase.title": "Compra", "purchase.create.title": "<PERSON><PERSON><PERSON>", "purchase.edit.title": "<PERSON>ar compra", "purchase.select.warehouse.label": "<PERSON><PERSON><PERSON><PERSON>", "purchase.select.warehouse.placeholder.label": "<PERSON><PERSON>", "purchase.select.supplier.label": "<PERSON><PERSON><PERSON><PERSON>", "purchase.select.supplier.placeholder.label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "purchase.select.status.label": "Estado", "purchase.select.status.placeholder.label": "<PERSON><PERSON>", "purchase.order-item.table.label": "<PERSON><PERSON><PERSON>", "purchase.order-item.table.net-unit-cost.column.label": "Costo unitario neto", "purchase.order-item.table.stock.column.label": "Valores", "purchase.order-item.table.qty.column.label": "Cantidad", "purchase.order-item.table.discount.column.label": "Descuento", "purchase.order-item.table.tax.column.label": "Impuesto", "purchase.order-item.table.sub-total.column.label": "Total parcial", "purchase.input.order-tax.label": "Impuesto de pedido", "purchase.input.shipping.label": "Envío", "purchase.placeholder.notes.input": "Introducir notas", "purchase.product-list.validate.message": "Por favor agregue el producto a la lista", "purchase.select.warehouse.validate.label": "Por favor seleccione almacén", "purchase.select.supplier.validate.label": "Por favor seleccione proveedor", "purchase.input.discount.validate.label": "Por favor ingrese el descuento", "purchase.input.order-tax.validate.label": "Por favor, introduzca el impuesto del pedido", "purchase.input.shipping.validate.label": "Por favor ingrese el envío", "purchase.product-modal.select.discount-type.label": "Tipo de descuento", "discount-type.filter.percentage.label": "Po<PERSON>entaj<PERSON>", "discount-type.filter.fixed.label": "<PERSON><PERSON><PERSON>", "purchase.table.column.reference-code.label": "Código de referencia", "purchase.grant-total.label": "Gran total", "purchase.success.create.message": "Compra creada con éxito", "purchase.success.edit.message": "Compra actualizada con éxito", "purchase.success.delete.message": "Compra eliminada con éxito", "purchases.details.title": "Detalle de compra", "purchase.detail.supplier.info": "Información del proveedor", "purchase.detail.purchase.info": "Información de compra", "purchases.return.title": "Compras Devoluciones", "purchase.return.create.title": "Crear devolución de compra", "purchase.return.edit.title": "Editar devolución de compra", "purchase.return.success.create.message": "Devolución de compra creada con éxito", "purchase.return.success.edit.message": "Devolución de compra actualizada con éxito", "purchase.return.success.delete.message": "Devolución de compra eliminada con éxito", "purchases.return.details.title": "Detalles de devolución de compra", "settings.title": "<PERSON><PERSON><PERSON><PERSON>", "settings.system-settings.title": "Ajustes del sistema", "settings.system-settings.select.default-currency.label": "<PERSON><PERSON> predeterminada", "settings.system-settings.select.default-currency.placeholder.label": "Elija la moneda predeterminada", "settings.system-settings.input.default-email.label": "Correo electrónico predeterminado", "settings.system-settings.input.default-email.placeholder.label": "Ingrese el correo electrónico predeterminado", "settings.system-settings.select.default-language.label": "Idioma predeterminado", "settings.system-settings.select.default-language.placeholder.label": "Elija el idioma predeterminado", "settings.system-settings.select.default-customer.label": "Cliente predeterminado", "settings.system-settings.select.default-customer.placeholder.label": "Elegir cliente predeterminado", "settings.system-settings.select.default-warehouse.label": "Almacén predeterminado", "settings.system-settings.select.default-warehouse.placeholder.label": "Elegir almacén predeterminado", "settings.system-settings.input.change-logo.label": "Cambiar logotipo", "settings.system-settings.input.company-name.label": "nombre de empresa", "settings.system-settings.input.company-name.placeholder.label": "Introduzca el nombre de la empresa", "settings.system-settings.input.company-phone.label": "Teléfono de la empresa", "settings.system-settings.input.company-phone.placeholder.label": "Introduzca el teléfono de la empresa", "settings.system-settings.input.developed-by.label": "Desarrollado por", "settings.system-settings.input.developed-by.placeholder.label": "Entra Desarrollado por", "settings.system-settings.input.footer.label": "Pie de página", "settings.system-settings.input.footer.placeholder.label": "Ingresar pie de página", "settings.payment-gateway.title": "Pasarela de pago", "settings.payment-gateway.input.stripe-key.label": "CLAVE DE RAYA", "settings.payment-gateway.input.stripe-key.placeholder.label": "Deje este campo en blanco si no lo ha cambiado.", "settings.payment-gateway.input.stripe-secret.label": "SECRETO DE LA RAYA", "settings.payment-gateway.switch-btn.label": "Eliminar claves API de Stripe", "settings.sms-configuration.title": "Configuración SMS", "settings.sms-configuration.select.sms-gateway.label": "Puerta de enlace de SMS", "settings.sms-configuration.select.sms-gateway.placeholder.label": "Elija la puerta de enlace de SMS", "settings.sms-configuration.input.twilio-sid.label": "TWILIO SID", "settings.sms-configuration.input.twilio-token.label": "FICHA TWILIO", "settings.sms-configuration.select.twilio-from.label": "TWILIO DESDE", "settings.sms-configuration.select.twilio-from.placeholder.label": "Entra TWILIO DESDE", "settings.sms-configuration.input.twilio-sid.placeholder.label": "Introduzca TWILIO SID", "settings.smtp-configuration.title": "Configuración SMTP", "settings.smtp-configuration.input.host.label": "ANFITRIÓN", "settings.smtp-configuration.input.port.label": "PUERTO", "settings.smtp-configuration.input.username.label": "Nombre de usuario", "settings.smtp-configuration.input.password.label": "Clave", "settings.smtp-configuration.input.encryption.label": "Cifrado", "settings.clear-cache.title": "Limpiar cache", "settings.system-settings.select.default-currency.validate.label": "Seleccione la moneda", "settings.system-settings.input.company-name.validate.label": "Por favor, introduzca el nombre de la empresa", "settings.system-settings.input.company-phone.validate.label": "Por favor, introduzca el teléfono de la empresa", "settings.system-settings.input.developed-by.validate.label": "Por favor ingrese desarrollado por", "settings.system-settings.input.footer.validate.label": "por favor ingrese la ciudad", "settings.system-settings.select.default-language.validate.label": "Por favor selecciona idioma", "settings.system-settings.select.default-customer.validate.label": "Por favor seleccione cliente", "settings.system-settings.select.default-warehouse.validate.label": "Por favor seleccione almacén", "settings.system-settings.select.address.validate.label": "Por favor ingrese la dirección", "settings.system-settings.select.address.valid.validate.label": "La dirección no debe tener más de 150 caracteres", "settings.sms-configuration.select.sms-gateway.validate.label": "Seleccione la puerta de enlace SMS", "settings.sms-configuration.input.twilio-sid.validate.label": "Por favor ingrese sid", "settings.sms-configuration.input.twilio-token.validate.label": "Por favor ingrese token", "settings.sms-configuration.select.twilio-from.validate.label": "Please enter twillo from", "settings.smtp-configuration.input.host.validate.label": "Por favor ingrese el host smtp", "settings.smtp-configuration.input.port.validate.label": "Por favor ingrese el puerto smtp", "settings.smtp-configuration.input.username.validate.label": "Por favor ingrese nombre de usuario", "settings.smtp-configuration.input.password.validate.label": "Por favor, ingrese contraseña", "settings.smtp-configuration.input.encryption.validate.label": "Por favor ingrese encriptación", "settings.success.edit.message": "Configuración actualizada con éxito", "update-profile.input.full-name.label": "Nombre completo", "update-profile.title": "detalles del perfil", "update-profile.tab.title": "Actualización del perfil", "update-profile.success.update.message": "perfil actualizado con éxito", "change-password.input.current.label": "contraseña actual", "change-password.input.new.label": "Nueva contraseña", "change-password.input.confirm.label": "<PERSON><PERSON>", "change-password.input.current.placeholder.label": "Introducir la contraseña actual", "change-password.input.new.placeholder.label": "Ingrese nueva clave", "change-password.input.confirm.placeholder.label": "Ingresar Confirmar contrase<PERSON>", "change-password.input.current.validate.label": "Introducir la contraseña actual", "change-password.input.new.validate.label": "Ingrese nueva clave", "change-password.input.confirm.validate.label": "Ingrese confirmar contraseña", "change-password.input.confirm.valid.validate.label": "La confirmación de la contraseña no coincide", "login-form.title": "In<PERSON><PERSON>", "login-form.login-btn.label": "Acceso", "login-form.forgot-password.label": "Has olvidado tu contraseña ?", "forgot-password-form.reset-link-btn.label": "Enviar enlace de restablecimiento de contraseña", "forgot-password-form.success.reset-link.label": "¡Le hemos enviado un correo electrónico con su enlace de restablecimiento de contraseña!", "login.success.message": "Iniciado sesión con éxito.", "logout.success.message": "Cerrar sesión con éxito.", "change-language.update.success.message": "Idioma actualizado correctamente", "reset-password.title": "Restablecer la contraseña", "reset-password.password.validate.label": "La contraseña de confirmación y la contraseña deben coincidir", "reset-password.success.update.message": "¡Tu contraseña ha sido restablecida!", "sales.title": "Ventas", "sale.title": "Rebaja", "sale.create.title": "<PERSON><PERSON>r venta", "sale.edit.title": "<PERSON><PERSON> venta", "sale.select.customer.label": "Cliente", "sale.select.customer.placeholder.label": "Elegir Cliente", "sale.select.customer.validate.label": "Por favor seleccione cliente", "sale.select.payment-status.placeholder": "Elija el estado de pago", "sale.order-item.table.net-unit-price.column.label": "Precio Unitario Neto", "sale.product.table.no-data.label": "Datos no disponibles", "sale.success.create.message": "Venta creada con éxito", "sale.success.edit.message": "Venta actualizada con éxito", "sale.success.delete.message": "Venta eliminada con éxito", "sale.details.title": "Detalle de venta", "sale.detail.customer.info": "Información del cliente", "sale.detail.invoice.info": "Información de la factura", "sales-return.title": "Devoluciones de ventas", "sale-return.title": "Devolución de venta", "sale-return.create.title": "Crear devolución de venta", "sale-return.edit.title": "Editar devolución de venta", "sale-return.success.create.message": "Devolución de venta creada con éxito", "sale-return.success.edit.message": "Devolución de venta actualizada con éxito", "sale-return.success.delete.message": "Devolución de venta eliminada con éxito", "sale-return.details.title": "Detalles de devolución de venta", "date-picker.filter.today.label": "Hoy dia", "date-picker.filter.this-week.label": "<PERSON><PERSON> semana", "date-picker.filter.last-week.label": "La semana pasada", "date-picker.filter.this-month.label": "<PERSON>ste mes", "date-picker.filter.last-month.label": "El mes pasado", "date-picker.filter.Custom-Range.label": "<PERSON><PERSON>", "date-picker.filter.reset.label": "Reiniciar", "date-picker.filter.apply.label": "Aplicar", "date-picker.filter.placeholder.label": "Seleccione fecha", "bar.title": "Bar", "line.title": "Lín<PERSON>", "filter.label": "Filtrar", "reports.title": "Reporte", "warehouse.reports.title": "Informe de almacén", "sale.reports.title": "Informe de venta", "stock.reports.title": "Informe de existencias", "purchase.reports.title": "Informe de compra", "top-selling-product.reports.title": "Informe de productos más vendidos", "globally.react-table.column.code.label": "Código", "print.barcode.title": "Imprimir c<PERSON> de <PERSON>", "paper.size.title": "<PERSON><PERSON><PERSON>", "paper.size.placeholder.label": "Elija el tamaño del papel", "globally.paper.size.validate.label": "Seleccione el tamaño del papel", "print.validate.label": "Actualice el código de barras para imprimir", "current.stock.label": "Stock actual", "stock.report.details.title": "Detalles del informe de existencias", "print.title": "Impresión", "update.title": "Actualizar", "preview.title": "Avance", "toast.successful.title": "Exitoso", "toast.error.title": "¡Algo salió mal!", "unit.filter.all.label": "Todos", "unit.filter.piece.label": "Pieza", "unit.filter.meter.label": "Metro", "unit.filter.kilogram.label": "Kilogramo", "status.filter.received.label": "Recibió", "status.filter.pending.label": "Pendiente", "status.filter.ordered.label": "Ordenado", "payment-status.filter.paid.label": "<PERSON><PERSON>", "payment-status.filter.unpaid.label": "No pagado", "excel.btn.label": "SOBRESALIR", "pdf.btn.label": "PDF", "cash.label": "<PERSON><PERSON>", "no-option.label": "Sin opciones", "warehouse.details.title": "detalles del almacén", "select.payment-type.label": "Tipo de pago", "payment-type.filter.cheque.label": "Controlar", "payment-type.filter.bank-transfer.label": "Transferencia bancaria", "payment-type.filter.other.label": "Otra", "paying-amount-title": "Monto de pago", "create-payment-title": "Crear pago", "reference-placeholder-label": "Introducir referencia", "input-Amount-to-pay-title": "Monto a pagar", "edit-payment-title": "Editar pago", "no-product-found.label": "No se encontró ningún producto", "sale.select.payment-type.placeholder": "Elija el tipo de pago", "globally.payment.type.validate.label": "Seleccione el tipo de pago", "pos.payment.amount.exceeds.total.error": "El monto del pago no puede exceder el monto total", "pos.payment.total.exceeds.grand.total.error": "El monto total del pago excede el gran total", "globally.payment.details.validate.label": "Por favor revise los detalles de pago para errores", "product.quantity.alert.reports.title": "Alertas de cantidad de producto", "globally-saving-btn-label": "Ahorro...", "payment-status.filter.partial.label": "Parcial", "dashboard.recentSales.total-product.label": "Productos totales", "adjustments.title": "<PERSON><PERSON><PERSON><PERSON>", "adjustments.create.title": "<PERSON><PERSON><PERSON>", "adjustments.edit.title": "<PERSON><PERSON>", "adjustments.detail.title": "Detalles de ajuste", "Adjustment.success.create.message": "Ajuste creado correctamente", "Adjustment.success.edit.message": "Ajuste actualizado correctamente", "Adjustment.success.delete.message": "Ajuste eliminado con éxito", "login-form.go-to-sign-in.label": "Volver a iniciar sesión", "pos-product.title": "PRODUCTO", "pos-qty.title": "CANTIDAD", "pos-price.title": "PRECIO", "pos-sub-total.title": "SUB TOTAL", "pos-total-qty.title": "Calidad Total", "pos-total.title": "Total", "pos-pay-now.btn": "<PERSON><PERSON> ahora", "pos-globally.search.field.label": "Escanear/buscar producto por nombre de código", "pos-make-Payment.title": "Hacer el pago", "pos-received-amount.title": "Cantidad recibida", "pos-total-amount.title": "Cantidad total", "pos-sale.detail.invoice.info": "Factura", "pos-sale.detail.Phone.info": "Teléfono", "pos-sale.detail.Paid-bt.title": "<PERSON><PERSON> por", "pos-close-btn.title": "Cerca", "pos-item.print.invoice.title": "<PERSON><PERSON><PERSON><PERSON>", "pos-all.categories.label": "todas las categorias", "pos-all.brands.label": "Todas las marcas", "pos-no-product-available.label": "No hay productos disponibles", "pos-sale.select.discount-type.placeholder": "Elija el tipo de descuento", "pos-sale.select.sale-unit-type.placeholder": "Elija el tipo de unidad de venta", "pos-thank.you-slip.invoice": "<PERSON><PERSON><PERSON> por comprar con nosotros. Por favor visite de nuevo", "pos.payment.success.message": "Pago realizado con éxito", "pos.cash-payment.product-error.message": "Por favor agregue el producto al carrito", "pos.cash-payment.quantity-error.message": "Favor de agregar cantidad de producto", "pos.cash-payment.tax-error.message": "Ingrese el valor del impuesto entre 0 y 100", "pos.cash-payment.total-amount-error.message": "El monto del descuento no debe ser mayor que el total", "pos.subtotal.small.title": "<PERSON>", "settings.system-settings.select.default-version-footer.placeholder.label": "Mostrar el número de versión en el pie de página", "settings.system-settings.select.logo.placeholder.label": "Mostrar logo en comprobante de pago", "settings.system-settings.select.appname-sidebar.placeholder.label": "Mostrar el nombre de la aplicación en la barra lateral", "pos.cash-payment.sub-total-amount-error.message": "El monto del envío no debe ser mayor que el subtotal", "product.import.title": "Productos de importación", "globally.sample.download.label": "<PERSON><PERSON><PERSON> muestra", "product-code.import.required-highlight.message": "el código debe no existir ya", "product-unit.import.required-highlight.message": "la unidad ya debe estar creada Utilice el nombre completo de la unidad", "globally.optional-input.validate.label": "Campo opcional", "reset.title": "Reiniciar", "reset.yes.title": "Sí, restablecer", "dashboard.widget.today-total-purchases.label": "Compras totales hoy", "dashboard.widget.today-payment-received.label": "Total recibido hoy (ventas)", "dashboard.widget.today-total-sales.label": "Ventas totales hoy", "dashboard.widget.today-total-expense.label": "Gasto total de hoy", "reset.modal.msg": "¿Seguro que quieres restablecer", "globally.file.validate.label": "Seleccione el archivo", "globally.csv-file.validate.label": "Seleccione el archivo csv", "file.success.upload.message": "El archivo ha subido correctamente", "transfers.title": "Transferencias", "transfer.title": "Transferir", "transfer.create.title": "<PERSON><PERSON><PERSON>", "transfer.edit.title": "Editar <PERSON>", "transfer.from-warehouse.title": "<PERSON><PERSON>", "transfer.to-warehouse.title": "Al Almacén", "transfer.success.create.message": "Transferencia creada con éxito", "transfer.success.edit.message": "Transferencia actualizada con éxito", "transfer.success.delete.message": "Transferencia eliminada con éxito", "transfer.select.warehouse.validate.message": "No puede transferir stock en el mismo almacén", "status.filter.complated.label": "Compilado", "status.filter.sent.label": "Enviado", "transfer.details.title": "Detalles de la transferencia", "settings.prefixes-settings.input.purchases.placeholder.label": "PU", "settings.prefixes-settings.input.purchases-return.placeholder.label": "relaciones públicas", "settings.prefixes-settings.input.sales.placeholder.label": "SL", "settings.prefixes-settings.input.salse-return.placeholder.label": "relaciones públicas", "settings.prefixes-settings.input.expense.placeholder.label": "EX", "settings.prefixes-settings.input.purchases.validate.label": "Por favor ingrese compras", "settings.prefixes-settings.input.purchases-return.validate.label": "Por favor ingrese devolución de compras", "settings.prefixes-settings.input.sales.validate.label": "Por favor ingrese ventas", "settings.prefixes-settings.input.salse-return.validate.label": "Por favor ingrese devolución de ventas", "settings.prefixes-settings.input.expense.validate.label": "Por favor ingrese el gasto", "side-menu.empty.message": "No se encontraron registros coincidentes", "product.export.title": "Productos de exportación", "globally.input.content.label": "Contenido", "email-template.edit.title": "Editar plantilla de correo electrónico", "email-template.title": "Plantillas de correo electrónico", "email-template.success.edit.message": "Las plantillas de correo electrónico se actualizaron correctamente.", "setting.state.lable": "Estado", "setting.postCode.lable": "Código postal", "settings.system-settings.select.state.validate.label": "Por favor seleccione estado", "settings.system-settings.select.country.validate.label": "Por favor seleccione país", "settings.system-settings.select.postcode.validate.label": "Por favor seleccione código postal", "pos.register-details.sell.title": "vender", "purchases.total.amount.title": "Importe total de las compras", "purchases-return.total.amount.title": "Importe total de devolución de compras", "supplier.report.details.title": "Detalles del informe del proveedor", "supplier.report.title": "Reporte de Proveedores", "prefix.title": "prefijos", "quotations.title": "cotizaciones", "create-quotation.title": "Crear cotización", "edit-quotation.title": "Editar cotización", "details-quotations.title": "Detalles de cotizaciones", "quotation.success.create.message": "Cotización creada con éxito", "quotation.success.edit.message": "Cotización actualizada con éxito", "quotation.success.delete.message": "Cotización eliminada con éxito", "settings.system-settings.select.date-format.label": "Formato de datos", "quotation.title": "Cita", "quotation.detail.invoice.info": "Información de cotización", "pepole.title": "Pueblos", "converted.status.label": "Convertido", "settings.system-settings.select.postcode.validate.length.label": "La longitud del código postal no debe ser superior a 8", "mail-settings.sender-name.title": "Nombre del remitente", "mail-settings.title": "Configuración de correo", "mail-settings.success.edit.message": "Configuración de correo actualizada con éxito", "best-customer.report.title": "mejores clientes", "sms-template.edit.title": "Editar plantilla de SMS", "sms-template.title": "Plantillas SMS", "sms-template.success.edit.message": "Plantilla de SMS actualizada con éxito.", "sms-content-variables.title": "VARIABLES DE CONTENIDO DE SMS", "email-content-variables.title": "VARIABLES DE CONTENIDO DEL CORREO ELECTRÓNICO", "sms-content-text.error.message": "Ha alcanzado el carácter máximo permitido 160.", "sms-content.error.message": "el contenido debe ser requerido", "profit-loss.reports.title": "Pérdida de beneficios", "global.revenue.title": "ingresos", "global.gross-profit.title": "<PERSON><PERSON><PERSON><PERSON> bruto", "global.payment-received.title": "<PERSON><PERSON> reci<PERSON>", "global.payment-sent.title": "Pago enviado", "global.net-payment.title": "Pago neto", "customer.report.details.title": "Detalles del informe del cliente", "customer.report.title": "Informes de clientes", "sale.payment.report.title": "Pago de venta", "sale.total.amount.title": "Importe total de la venta", "sale-return.total.amount.title": "Importe total de devolución de venta", "sale-Due.total.amount.title": "Importe total adeudado", "sale-paid.total.amount.title": "Cantidad total pagada", "sale-reference.title": "Referencia de venta", "more-report.option.title": "Más", "currency.icon.right.side.lable": "Icono de moneda Lado derecho", "total.sales.title": "Ventas totales", "email.status.edit.success.message": "Estado del correo electrónico actualizado con éxito.", "sms.status.edit.success.message": "Estado de SMS actualizado con éxito.", "sms-api.title": "API de SMS", "key.lable": "Llave", "key.value.lable": "<PERSON><PERSON> clave", "url.lable": "URL", "mobile.key.lable": "<PERSON><PERSON><PERSON> m<PERSON>", "message.key.lable": "Clave de mensaje", "sms.status.lable": "Estado SMS", "active.status.lable": "Activo", "in-active.status.lable": "Inactivo", "sms.api.update.success.message": "Actualización de la API de SMS Exitosamente.", "sale-return.product-qty.validate.message": "La cantidad devuelta es mayor que la cantidad vendida", "template.title": "Plantillas", "pos.product-quantity-error.message": "No hay más cantidad disponible.", "product-list.lable": "Lista de productos", "register.details.title": "Detalles del Registro", "register.total-sales.label": "Ventas Totales", "register.total-refund.title": "Reembolso total", "register.total-payment.title": "Pago Total", "register.product.sold.title": "Detalles de los productos vendidos", "register.product.sold.by.brand.title": "Detalles de los productos vendidos (por marca)", "print-barcode.show-company.label": "Mostrar nombre de la tienda", "print-barcode.show-product-name.label": "Mostrar nombre del producto", "print-barcode.show-price.label": "<PERSON><PERSON> precio", "product.input.quantity-limitation.label": "Limitación de cantidad", "product.input.quantity-limitation.placeholder": "Ingresar límite de cantidad", "pos.hold-list-btn.title": "<PERSON><PERSON><PERSON>", "create.hold-list.warning.message": "¿Retener factura? ¡La misma referencia reemplazará la lista anterior si existe!", "create-modal.yes.ok-btn": "Sí OK", "hold-list.reference-number.placeholder": "¡Ingrese el número de referencia!", "hold-list-id.table.column.label": "ID", "hold-list-ref-id.table.column.label": "ID de referencia", "hold-list.details.title": "Lista de espera", "hold-list.success.create.message": "Lista de espera creada con éxito", "hold-list.success.delete.message": "Lista de espera eliminada con éxito", "report-all.warehouse.label": "todo el almacén", "setting.mail-mailer.lable": "ENVÍO DE CORREO", "setting.mail-host.lable": "ANFITRIÓN DE CORREO", "setting.mail-port.lable": "PUERTO DE CORREO", "setting.mail-user-name.lable": "NOMBRE DE USUARIO DE CORREO", "setting.mail-password.lable": "CONTRASEÑA DE CORREO", "setting.mail-encryption.lable": "CIFRADO DE CORREO", "sale.payment.create.success": "Pago de venta creado con éxito", "sale.payment.edit.success": "Pago de venta actualizado con éxito", "paying-amount-validate-label": "El monto a pagar debe ser menor o igual al monto a pagar", "hold-list.reference-code.error": "El campo del código de referencia es obligatorio.", "settings.clear-cache.success.message": "borrado de caché con éxito", "product.product-in-stock.label": "En stock", "product-items.label": "Elementos", "Payload.key.lable": "Carga útil", "base-units.title": "Unidades base", "base-unit.create.title": "Crear unidad base", "base-unit.edit.title": "Editar unidad base", "base-unit.title": "Unidad base", "base-unit.success.create.message": "Unidad base creada con éxito", "base-unit.success.edit.message": "Unidad base actualizada con éxito", "base-unit.success.delete.message": "Unidad base eliminada con éxito", "DOB.input.label": "fecha de nacimiento", "purchase.product.quantity.validate.label": "Por favor ingrese la cantidad del producto", "languages.title": "Idiomas", "react-data-table.translation.column.label": "Traducción", "react-data-table.iso-date.column.label": "Código ISO", "globally.input.iso-code.validate.label": "Por favor ingrese el código ISO", "globally.input.iso-code.character.validate.label": "La longitud del código ISO debe ser igual a 2", "translation.manager.title": "<PERSON><PERSON><PERSON> traducci<PERSON>", "language.updated.success.message": "Idioma actualizado correctamente", "language.deleted.success.message": "Idioma eliminado con éxito", "language.edit.success.message": "Idioma editado correctamente", "language.save.success.message": "Idioma creado correctamente", "language.enabled.success.message": "Idioma habilitado con éxito", "language.disabled.success.message": "Idioma deshabilitado con éxito", "language.current-language-disable.error.message": "No puedes deshabilitar tu idioma actualmente seleccionado", "header.profile-menu.change-language.label": "Cambiar idioma", "language.title": "Idioma", "pos-paying-amount.title": "Monto de pago", "pos.change-return.label": "Cambio Devolución", "language.create.title": "Crear idioma", "purchase.less.recieving.ammout.error": "El monto recibido es más pequeño que el total general.", "add-stock.title": "<PERSON><PERSON><PERSON>", "product-quantity.add.title": "Agregar cantidad de producto", "edit-translation.title": "<PERSON><PERSON>", "globally.input.cash-in-hand.label": "Efectivo en mano", "globally.close-register.title": "<PERSON><PERSON><PERSON>", "register.closed.successfully.message": "Registro cerrado con éxito.", "register.entry.added.successfully.message": "Entrada de registro añadida correctamente.", "globally.total-cash.label": "Efectivo total", "globally.input.note.label": "<PERSON>a", "globally.input.note.placeholder.label": "Ingresar nota", "register.report.title": "Registrar Informe", "user-details.table.opened-on.row.label": "Abierto el", "user-details.table.closde-on.row.label": "Ce<PERSON>do el", "globally.input.cash-in-hand-while-closing.label": "Efectivo en mano mientras se cierra", "pos.cclose-register.enter-total-cash.message": "Agregue el efectivo total", "register.is.still.open.message": "¡El registro aún está abierto!", "Are.you.sure.you.want.to.go.to.dashboard.message": "¿Estás seguro de que quieres ir al Dashboard?", "product.quantity.title": "Cantidad del producto", "pos.this.product.out.of.stock.message": "Este producto está agotado", "pos.quantity.exceeds.quantity.available.in.stock.message": "La cantidad excede la cantidad disponible en stock", "yes.modal.title": "Sí", "no.modal.title": "No", "language.edit.title": "Editar idioma", "select.user.label": "Se<PERSON><PERSON><PERSON>r usuario", "suppliers.import.title": "Importar Proveedores", "customers.import.title": "Importar clientes", "products.type.single-type.label": "Único", "product.type.label": "Tipo de producto", "product.type.placeholder.label": "Elegir tipo de producto", "product.type.input.validation.error": "Seleccione el tipo de producto", "variation.name": "Nombre de la variante", "variation.variation_types": "Tipos de variante", "variations.title": "<PERSON><PERSON><PERSON>", "variation.title": "<PERSON><PERSON><PERSON>", "variation.create.title": "<PERSON><PERSON><PERSON>", "variation.edit.title": "<PERSON><PERSON>", "variation.input.name.label": "Nombre", "variation.input.name.placeholder.label": "Ingrese el nombre", "variation.input.name.validate.label": "Por favor, ingrese un nombre", "variation.success.create.message": "Variante creada con éxito", "variation.success.edit.message": "Variante editada con éxito", "variation.success.delete.message": "Variante eliminada con éxito", "variation.types.title": "Tipos de Variante", "variation.type.title": "<PERSON><PERSON><PERSON>", "variation.type.input.name.placeholder.label": "Por favor, ingrese el tipo de variante", "variation.type.input.name.validate.label": "Por favor, ingrese tipos de variante válidos", "variation.select.validation.error.message": "Por favor, seleccione una variante", "variation.type.select.validate.error.message": "Por favor, seleccione tipos de variante", "products.warehouse.title": "Almacén de productos", "barcode-symbol-uppercase-validation-message": "Ingrese solo letras mayúsculas y números para el código de barras Código 39.", "sale.product-qty.limit.validate.message": "No puedes comprar más que la cantidad límite.", "receipt-settings.title": "Configuración de recibo", "receipt-settings.show-warehouse.label": "Mostrar almacén", "receipt-settings.show-email.label": "Mostrar correo electrónico", "receipt-settings.show-address.label": "Mostrar dirección", "receipt-settings.show-customer.label": "Mostrar cliente", "receipt-settings.show-phone.label": "Mostrar teléfono", "receipt-settings.show-discount-shipping.label": "Mostrar descuento y envío", "receipt-settings.success.edit.message": "Configuración del recibo actualizada correctamente", "receipt-settigns.input.note.validate.label": "Por favor, introduzca la nota", "receipt-settings.show-barcode.label": "Mostrar código de barras en el recibo", "receipt-settings.show-note.label": "<PERSON><PERSON> nota", "globally.submit-and-print-button": "Enviar e imprimir", "receipt-settings.show-product-code.label": "Mostrar código de producto", "globally.footer.label": "Reservados todos los derechos", "logout.confirmation.label": "¿Está seguro de que desea cerrar sesión?", "addition.title": "<PERSON><PERSON>", "subtraction.title": "Resta", "print-custom-barcode.title": "Imprimir código de barras personalizado", "product.sku.label": "SKU/Código de barras", "add.stock.while.product.creation.title": "Añadir stock durante la creación del producto", "confirm-modal.msg": "¿Estás segura?", "store.title": "Tiendas", "create.store.title": "<PERSON><PERSON><PERSON> tienda", "edit.store.title": "<PERSON><PERSON> tienda", "store.name.title": "Nombre de la tienda", "store.name.placeholder.title": "Introduzca el nombre de la tienda", "store.name.validate.label": "Introduzca el nombre de la tienda", "store.success.create.message": "Tienda creada correctamente", "store.success.edit.message": "Tienda actualizada correctamente", "store.success.delete.message": "Tienda eliminada correctamente", "store.changed.message": "Tienda modificada correctamente", "store.header.name.title": "Tienda", "select.all.store.title": "Seleccionar todas las tiendas", "store.field.must.required.validate": "El campo de la tienda es obligatorio", "store.assigned.title": "Tiendas asignadas", "no.store.title": "No información de la tienda", "paid.amount.title": "Importe pagado", "taxes.title": "Impuestos", "tax.title": "Impuesto", "add.tax.title": "<PERSON><PERSON><PERSON>", "edit.tax.title": "Editar impuesto", "tax.name.title": "Nombre del impuesto", "tax.name.placeholder.title": "Introduzca el nombre del impuesto", "tax.name.validate.title": "Introduzca el nombre del impuesto", "tax.value.title": "Valor del impuesto", "tax.value.placeholder.title": "Ingrese el valor del impuesto", "tax.value.validate.title": "Ingrese el valor del impuesto", "tax.deleted.success.message": "Impuesto eliminado correctamente", "tax.edit.success.message": "Impuesto editado correctamente", "tax.save.success.message": "Impuesto creado correctamente", "tax.name.unique.validate.title": "El nombre del impuesto ya existe", "tax.value.unique.validate.title": "El valor del impuesto ya existe", "tax.show.on.receipt.pdf.title": "Mostrar en el recibo/PDF", "pos.settings.title": "Configuración del TPV", "enable.pos.sound.title": "Activar sonido de clic del TPV", "show.out.of.stock.product.in.pos": "Mostrar productos agotados en el TPV", "pos.sound.title": "Sonido TPV", "upload.audio.title": "Subir audio", "pos.audio.required": "Por favor, suba el audio", "date.of.birth.title": "Fecha de nacimiento", "customer.details.title": "Datos del cliente", "pos.audio.length.tooltip.title": "La duración del audio debe ser inferior a 3 segundos.", "select.date.of.birth": "Wählen Sie Ihr Geburtsdatum", "item.deleted.success.message": "Elemento eliminado correctamente", "payment.method.save.success.message": "Método de pago creado correctamente", "payment.method.edit.success.message": "Método de pago editado correctamente", "payment.method.deleted.success.message": "Método de pago eliminado correctamente", "payment.method.name.unique.validate.title": "El nombre del método de pago ya existe", "show.tax.title": "Mostrar impuestos", "expiry.date.title": "<PERSON><PERSON> de caducidad", "expiry.date.placeholder.title": "Ingrese la fecha de caducidad", "payment.method.title": "Método de pago", "dual.screen.settings.title": "Configuración de pantalla dual", "dual.screen.display.header.title": "Encabezado de pantalla", "dual.screen.display.header.placeholder.title": "Introducir encabezado de pantalla", "please.enter.display.header.title": "Introducir encabezado de pantalla", "carousel.image.title": "Imágenes del carrusel", "validation.you.can.upload.maximum.images": "Puedes subir un máximo de 5 imágenes", "upload.maximum.images": "Sube un máximo de cinco imágenes.", "no.customer.selected.title": "No hay ningún cliente seleccionado", "send.test.email.title": "Enviar correo electrónico de prueba", "send.test.email.success.message": "Correo electrónico de prueba enviado correctamente.", "globally.receipt.download.label": "<PERSON><PERSON><PERSON> recibo", "load.more.title": "<PERSON>gar más", "pos.edit.sale.title": "¿Estás segura de que quieres editar esta venta?", "sale.payment.total-exceed.validate.message": "El importe total del pago no debe superar el total general.", "globally.input.width.label": "<PERSON><PERSON>", "globally.input.height.label": "Altura", "globally.input.width.validate.label": "Por favor, introduzca el ancho", "globally.input.height.validate.label": "Por favor, introduzca la altura"}