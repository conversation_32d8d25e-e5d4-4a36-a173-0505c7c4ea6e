import React from "react";
import { Image } from "react-bootstrap-v5";
import {
    currencySymbolHandling,
    getFormattedMessage,
} from "../../shared/sharedMethod";

class ThermalPrintButton extends React.PureComponent {
    render() {
        const print = this.props.updateProducts;
        const frontSetting = this.props.frontSetting;
        const allConfigData = this.props.allConfigData;
        const barcodeOptions = this.props.barcodeOptions;

        const companyName = allConfigData && allConfigData.store_name;
        const currencySymbol =
            frontSetting &&
            frontSetting.value &&
            frontSetting.value.currency_symbol;

        function printFunction(product, index) {
            let indents = [];
            for (let i = 0; i < product.quantity; i++) {
                indents.push(
                    <div
                        key={i}
                        className="thermal-barcode-item"
                    >
                        <div className="thermal-barcode-content">
                            {barcodeOptions.companyName && companyName && (
                                <div className="thermal-company-name">
                                    {companyName}
                                </div>
                            )}
                            {barcodeOptions.productName && product.name && (
                                <div className="thermal-product-name">
                                    {product.name}
                                </div>
                            )}
                            {barcodeOptions?.price && product.product_price && (
                                <div className="thermal-price">
                                    <span className="thermal-price-label">
                                        {getFormattedMessage(
                                            "product.table.price.column.label"
                                        )}
                                        :
                                    </span>{" "}
                                    {currencySymbolHandling(
                                        allConfigData,
                                        currencySymbol,
                                        product.product_price
                                    )}
                                </div>
                            )}
                            <div className="thermal-barcode-image">
                                <Image
                                    src={product && product.barcode_url}
                                    alt={product && product.name}
                                    className="thermal-barcode-img"
                                />
                            </div>
                            <div className="thermal-barcode-code">
                                {product && product.code}
                            </div>
                        </div>
                    </div>
                );
            }
            return indents;
        }

        return (
            <div className="thermal-print-container">
                {print.products &&
                    print.products.map((product, index) => {
                        return printFunction(product, index);
                    })}
            </div>
        );
    }
}

export default ThermalPrintButton;
